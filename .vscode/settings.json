{"java.jdt.ls.vmargs": "-XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx128G -Xms100m -Xlog:disable", "maven.executable.path": "/Users/<USER>/Downloads/apache-maven-3.8.6/bin/mvn", "java.configuration.maven.userSettings": "/Users/<USER>/.m2/settings.xml", "maven.terminal.useJavaHome": true, "maven.terminal.customEnv": [{"environmentVariable": "MAVEN_HOME", "value": "/Users/<USER>/Downloads/apache-maven-3.8.6"}, {"environmentVariable": "JAVA_HOME", "value": "/Library/Java/JavaVirtualMachines/temurin-17.jdk/Contents/Home"}], "java.compile.nullAnalysis.mode": "automatic", "java.configuration.runtimes": [{"name": "JavaSE-17", "path": "/Library/Java/JavaVirtualMachines/temurin-17.jdk/Contents/Home"}], "maven.view": "hierarchical"}