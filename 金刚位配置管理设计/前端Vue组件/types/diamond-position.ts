/**
 * 图标类型
 */
export type IconType = 'url' | 'svg' | 'base64' | 'emoji'

/**
 * 金刚位数据类型定义
 */
export interface DiamondPosition {
  /** 主键ID */
  id?: number

  /** 金刚位名称 */
  name: string

  /** 图标内容 */
  icon: string

  /** 图标类型 */
  iconType: IconType

  /** 跳转链接 */
  url: string

  /** 排序顺序 */
  sortOrder: number

  /** 状态：0-禁用，1-启用 */
  status: 0 | 1

  /** 描述信息 */
  description?: string

  /** 创建时间 */
  createdTime?: string

  /** 更新时间 */
  updatedTime?: string

  /** 创建人 */
  createdBy?: string

  /** 更新人 */
  updatedBy?: string
}

/**
 * 金刚位创建请求参数
 */
export interface CreateDiamondPositionRequest {
  name: string
  icon: string
  iconType: IconType
  url: string
  sortOrder: number
  status: 0 | 1
  description?: string
}

/**
 * 金刚位更新请求参数
 */
export interface UpdateDiamondPositionRequest extends Partial<CreateDiamondPositionRequest> {
  id: number
}

/**
 * 金刚位排序更新请求参数
 */
export interface UpdateSortOrderRequest {
  id: number
  sortOrder: number
}

/**
 * 金刚位列表查询参数
 */
export interface DiamondPositionQuery {
  /** 搜索关键词 */
  keyword?: string

  /** 状态筛选 */
  status?: 0 | 1

  /** 图标类型筛选 */
  iconType?: IconType

  /** 页码 */
  page?: number

  /** 每页数量 */
  pageSize?: number
}

/**
 * 分页响应数据
 */
export interface PaginatedResponse<T> {
  /** 数据列表 */
  data: T[]
  
  /** 总数量 */
  total: number
  
  /** 当前页码 */
  page: number
  
  /** 每页数量 */
  pageSize: number
  
  /** 总页数 */
  totalPages: number
}

/**
 * API响应基础结构
 */
export interface ApiResponse<T = any> {
  /** 是否成功 */
  success: boolean
  
  /** 响应数据 */
  data: T
  
  /** 响应消息 */
  message: string
  
  /** 错误代码 */
  code?: string
}

/**
 * 图片上传响应数据
 */
export interface ImageUploadResponse {
  /** 文件URL（type=url时返回） */
  url?: string

  /** Base64编码（type=base64时返回） */
  base64?: string

  /** 原始文件名 */
  originalName: string

  /** 文件大小 */
  size: number

  /** MIME类型 */
  mimeType: string
}

/**
 * SVG上传响应数据
 */
export interface SvgUploadResponse {
  /** SVG内容 */
  svgContent: string

  /** 原始文件名 */
  originalName: string

  /** 文件大小 */
  size: number
}

/**
 * 文件上传参数
 */
export interface FileUploadParams {
  /** 文件对象 */
  file: File

  /** 上传类型 */
  type: 'url' | 'base64'
}

/**
 * 图标验证规则
 */
export interface IconValidationRule {
  /** 图标类型 */
  type: IconType

  /** 验证正则表达式 */
  pattern?: RegExp

  /** 最大长度 */
  maxLength?: number

  /** 错误提示信息 */
  message: string
}

/**
 * 设备类型
 */
export type DeviceType = 'mobile' | 'tablet' | 'desktop'

/**
 * 拖拽状态
 */
export interface DragState {
  /** 拖拽中的元素ID */
  draggedId: number | null
  
  /** 拖拽目标元素ID */
  targetId: number | null
  
  /** 是否正在拖拽 */
  isDragging: boolean
}