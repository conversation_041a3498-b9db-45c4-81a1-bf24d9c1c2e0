<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑金刚位' : '新增金刚位'"
    width="600px"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      label-position="left"
    >
      <el-form-item label="金刚位名称" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入金刚位名称"
          maxlength="20"
          show-word-limit
          clearable
        />
      </el-form-item>

      <el-form-item label="图标类型" prop="iconType">
        <el-select
          v-model="form.iconType"
          placeholder="请选择图标类型"
          @change="handleIconTypeChange"
          style="width: 100%"
        >
          <el-option label="URL链接" value="url" />
          <el-option label="表情符号" value="emoji" />
          <el-option label="SVG代码" value="svg" />
          <el-option label="Base64编码" value="base64" />
        </el-select>
      </el-form-item>

      <el-form-item label="图标内容" prop="icon">
        <!-- URL类型 -->
        <div v-if="form.iconType === 'url'" class="icon-input-group">
          <div class="input-with-upload">
            <el-input
              v-model="form.icon"
              placeholder="请输入图标URL地址，如：https://example.com/icon.png"
              @input="updateIconPreview"
            />
            <el-upload
              :action="uploadAction"
              :show-file-list="false"
              :on-success="handleImageUploadSuccess"
              :on-error="handleUploadError"
              :before-upload="beforeImageUpload"
              accept="image/*"
              style="margin-left: 8px"
            >
              <el-button type="primary" size="small" :loading="uploading">
                <el-icon><Upload /></el-icon>
                上传图片
              </el-button>
            </el-upload>
          </div>
          <div class="form-help">支持网络链接、本地路径或上传图片文件</div>
        </div>

        <!-- 表情符号类型 -->
        <div v-else-if="form.iconType === 'emoji'" class="icon-input-group">
          <el-input
            v-model="form.icon"
            placeholder="请输入表情符号，如：💰 🔧 💳"
            maxlength="10"
            show-word-limit
            @input="updateIconPreview"
          />
          <div class="form-help">支持Unicode表情符号，长度不超过10个字符</div>
        </div>

        <!-- SVG类型 -->
        <div v-else-if="form.iconType === 'svg'" class="icon-input-group">
          <div class="textarea-with-upload">
            <el-input
              v-model="form.icon"
              type="textarea"
              :rows="4"
              placeholder="请输入SVG代码，如：<svg viewBox=&quot;0 0 24 24&quot;>...</svg>"
              @input="updateIconPreview"
            />
            <el-upload
              :action="uploadAction"
              :show-file-list="false"
              :on-success="handleSvgUploadSuccess"
              :on-error="handleUploadError"
              :before-upload="beforeSvgUpload"
              accept=".svg,image/svg+xml"
              class="upload-btn-overlay"
            >
              <el-button type="primary" size="small" :loading="uploading">
                <el-icon><Upload /></el-icon>
                上传SVG
              </el-button>
            </el-upload>
          </div>
          <div class="form-help">请输入完整的SVG代码或上传SVG文件</div>
        </div>

        <!-- Base64类型 -->
        <div v-else-if="form.iconType === 'base64'" class="icon-input-group">
          <div class="textarea-with-upload">
            <el-input
              v-model="form.icon"
              type="textarea"
              :rows="3"
              placeholder="请输入Base64编码，如：data:image/png;base64,iVBORw0..."
              @input="updateIconPreview"
            />
            <el-upload
              :action="uploadAction"
              :show-file-list="false"
              :on-success="handleBase64UploadSuccess"
              :on-error="handleUploadError"
              :before-upload="beforeImageUpload"
              accept="image/*"
              class="upload-btn-overlay"
            >
              <el-button type="primary" size="small" :loading="uploading">
                <el-icon><Upload /></el-icon>
                上传图片
              </el-button>
            </el-upload>
          </div>
          <div class="form-help">请输入完整的Base64编码或上传图片文件</div>
        </div>

        <!-- 图标预览 -->
        <div class="icon-preview-container">
          <div class="preview-label">图标预览：</div>
          <div class="preview-content">
            <div v-if="iconPreview.show" class="preview-icon">
              <img v-if="form.iconType === 'url' || form.iconType === 'base64'"
                   :src="form.icon"
                   alt="图标预览"
                   @error="iconPreview.show = false" />
              <div v-else-if="form.iconType === 'svg'"
                   v-html="form.icon"></div>
              <span v-else-if="form.iconType === 'emoji'">{{ form.icon }}</span>
            </div>
            <span v-else class="preview-placeholder">请选择图标类型并输入内容</span>
          </div>
        </div>
            <p>• 文件大小不超过 2MB</p>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="跳转链接" prop="url">
        <el-input
          v-model="form.url"
          placeholder="请输入跳转链接，如：https://example.com 或 /page/detail"
          clearable
        >
          <template #prepend>
            <el-select v-model="linkType" style="width: 100px">
              <el-option label="HTTP" value="http" />
              <el-option label="路由" value="route" />
            </el-select>
          </template>
        </el-input>
        <div class="link-preview" v-if="form.url">
          <span class="link-label">预览：</span>
          <a :href="form.url" target="_blank" class="link-url">{{ form.url }}</a>
        </div>
      </el-form-item>

      <el-form-item label="显示状态" prop="status">
        <el-switch
          v-model="statusEnabled"
          active-text="启用"
          inactive-text="禁用"
          active-color="#13ce66"
          inactive-color="#ff4949"
          @change="handleStatusChange"
        />
        <div class="status-tips">
          禁用后该金刚位将不会在前端显示
        </div>
      </el-form-item>

      <el-form-item label="排序权重" prop="sortOrder">
        <el-input-number
          v-model="form.sortOrder"
          :min="1"
          :max="999"
          controls-position="right"
          placeholder="排序权重"
        />
        <div class="sort-tips">
          数值越小排序越靠前，建议使用10的倍数便于后续调整
        </div>
      </el-form-item>

      <el-form-item label="描述信息">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="可选：输入金刚位的描述信息"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <!-- 预览区域 -->
    <div class="preview-section">
      <h4>预览效果</h4>
      <div class="diamond-preview">
        <div class="preview-item">
          <div class="preview-icon">
            <img v-if="form.iconUrl" :src="form.iconUrl" :alt="form.name" />
            <el-icon v-else class="placeholder-icon"><Picture /></el-icon>
          </div>
          <div class="preview-name">{{ form.name || '金刚位名称' }}</div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { Plus, Edit, Picture, Upload } from '@element-plus/icons-vue'
import type { DiamondPosition, IconType } from '@/types/diamond-position'
import { useIconHandler, useFileUpload } from '@/composables/useDiamondPosition'

// Props
interface Props {
  modelValue: boolean
  formData: Partial<DiamondPosition>
  isEdit: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  formData: () => ({}),
  isEdit: false
})

// Emits
interface Emits {
  'update:modelValue': [value: boolean]
  confirm: [formData: Partial<DiamondPosition>]
}

const emit = defineEmits<Emits>()

// 组合式函数
const { validateIcon, detectIconType } = useIconHandler()
const { uploading, uploadImage, uploadSvg } = useFileUpload()

// 响应式数据
const formRef = ref<FormInstance>()
const loading = ref(false)
const linkType = ref('http')
const uploadAction = ref('/api/diamond-position/upload/image')

// 表单数据
const form = reactive<Partial<DiamondPosition>>({
  name: '',
  icon: '',
  iconType: 'url',
  url: '',
  status: 1,
  sortOrder: 1,
  description: ''
})

// 图标预览状态
const iconPreview = reactive({
  show: false
})

// 状态开关计算属性
const statusEnabled = computed({
  get: () => form.status === 1,
  set: (value: boolean) => {
    form.status = value ? 1 : 0
  }
})

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value)
})

// 表单验证规则
const rules: FormRules = {
  name: [
    { required: true, message: '请输入金刚位名称', trigger: 'blur' },
    { min: 1, max: 20, message: '名称长度在 1 到 20 个字符', trigger: 'blur' }
  ],
  iconType: [
    { required: true, message: '请选择图标类型', trigger: 'change' }
  ],
  icon: [
    { required: true, message: '请输入图标内容', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (!value || !form.iconType) {
          callback()
          return
        }

        const validation = validateIcon(form.iconType, value)
        if (!validation.valid) {
          callback(new Error(validation.message))
          return
        }

        callback()
      },
      trigger: 'blur'
    }
  ],
  url: [
    { required: true, message: '请输入跳转链接', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (!value) {
          callback(new Error('请输入跳转链接'))
          return
        }

        // 验证HTTP链接格式
        if (linkType.value === 'http') {
          const httpRegex = /^https?:\/\/.+/
          if (!httpRegex.test(value)) {
            callback(new Error('请输入有效的HTTP链接，如：https://example.com'))
            return
          }
        }

        // 验证路由格式
        if (linkType.value === 'route') {
          const routeRegex = /^\/[a-zA-Z0-9\/_-]*$/
          if (!routeRegex.test(value)) {
            callback(new Error('请输入有效的路由路径，如：/page/detail'))
            return
          }
        }

        callback()
      },
      trigger: 'blur'
    }
  ],
  sortOrder: [
    { required: true, message: '请输入排序权重', trigger: 'blur' },
    { type: 'number', min: 1, max: 999, message: '排序权重范围为 1-999', trigger: 'blur' }
  ]
}

// 图标处理方法
const handleIconTypeChange = () => {
  // 清空图标内容
  form.icon = ''
  iconPreview.show = false
}

const updateIconPreview = () => {
  if (form.icon && form.iconType) {
    const validation = validateIcon(form.iconType, form.icon)
    iconPreview.show = validation.valid
  } else {
    iconPreview.show = false
  }
}

const handleStatusChange = (value: boolean) => {
  form.status = value ? 1 : 0
}

// 文件上传处理方法
const beforeImageUpload = (file: File) => {
  const isImage = file.type.startsWith('image/')
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!')
    return false
  }
  return true
}

const beforeSvgUpload = (file: File) => {
  const isSvg = file.type === 'image/svg+xml' || file.name.endsWith('.svg')
  const isLt1M = file.size / 1024 / 1024 < 1

  if (!isSvg) {
    ElMessage.error('只能上传SVG文件!')
    return false
  }
  if (!isLt1M) {
    ElMessage.error('SVG文件大小不能超过 1MB!')
    return false
  }
  return true
}

const handleImageUploadSuccess = async (file: File) => {
  try {
    const result = await uploadImage({ file, type: 'url' })
    if (result.url) {
      form.icon = result.url
      updateIconPreview()
      ElMessage.success('图片上传成功')
    }
  } catch (error) {
    ElMessage.error(error instanceof Error ? error.message : '上传失败')
  }
}

const handleBase64UploadSuccess = async (file: File) => {
  try {
    const result = await uploadImage({ file, type: 'base64' })
    if (result.base64) {
      form.icon = result.base64
      updateIconPreview()
      ElMessage.success('图片转换成功')
    }
  } catch (error) {
    ElMessage.error(error instanceof Error ? error.message : '转换失败')
  }
}

const handleSvgUploadSuccess = async (file: File) => {
  try {
    const result = await uploadSvg(file)
    form.icon = result.svgContent
    updateIconPreview()
    ElMessage.success('SVG文件读取成功')
  } catch (error) {
    ElMessage.error(error instanceof Error ? error.message : '读取失败')
  }
}

const handleUploadError = () => {
  ElMessage.error('文件上传失败，请重试')
}

// 监听表单数据变化
watch(
  () => props.formData,
  (newData) => {
    if (newData) {
      Object.assign(form, {
        name: '',
        iconUrl: '',
        linkUrl: '',
        isEnabled: true,
        sortOrder: 1,
        description: '',
        ...newData
      })
      
      // 根据链接类型设置linkType
      if (form.linkUrl) {
        linkType.value = form.linkUrl.startsWith('http') ? 'http' : 'route'
      }
    }
  },
  { immediate: true, deep: true }
)

// 监听链接类型变化
watch(linkType, (newType) => {
  if (form.linkUrl) {
    if (newType === 'http' && !form.linkUrl.startsWith('http')) {
      form.linkUrl = 'https://' + form.linkUrl.replace(/^\//, '')
    } else if (newType === 'route' && form.linkUrl.startsWith('http')) {
      form.linkUrl = '/'
    }
  }
})

// 事件处理函数
const handleClose = () => {
  visible.value = false
  // 重置表单
  formRef.value?.resetFields()
}

const handleConfirm = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    loading.value = true
    
    // 提交表单数据
    emit('confirm', { ...form })
  } catch (error) {
    ElMessage.error('请检查表单填写是否正确')
  } finally {
    loading.value = false
  }
}

// 图标上传相关函数
const beforeIconUpload = (file: File) => {
  const isValidType = ['image/jpeg', 'image/png', 'image/svg+xml'].includes(file.type)
  const isLt2M = file.size / 1024 / 1024 < 2
  
  if (!isValidType) {
    ElMessage.error('图标只能是 JPG、PNG、SVG 格式!')
    return false
  }
  
  if (!isLt2M) {
    ElMessage.error('图标大小不能超过 2MB!')
    return false
  }
  
  return true
}

const handleIconSuccess = (response: any) => {
  if (response.success) {
    form.iconUrl = response.data.url
    ElMessage.success('图标上传成功')
  } else {
    ElMessage.error(response.message || '图标上传失败')
  }
}

const handleIconError = () => {
  ElMessage.error('图标上传失败，请重试')
}
</script>

<style scoped lang="scss">
.icon-upload-container {
  display: flex;
  gap: 16px;
  align-items: flex-start;

  .icon-uploader {
    flex-shrink: 0;
    
    :deep(.el-upload) {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: all 0.2s ease;
      
      &:hover {
        border-color: #409eff;
      }
    }
  }

  .icon-preview {
    position: relative;
    width: 80px;
    height: 80px;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 4px;
    }
    
    .icon-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.2s ease;
      border-radius: 4px;
      
      .el-icon {
        color: white;
        font-size: 20px;
      }
    }
    
    &:hover .icon-overlay {
      opacity: 1;
    }
  }

  .icon-placeholder {
    width: 80px;
    height: 80px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #8c939d;
    
    .icon-upload-icon {
      font-size: 24px;
      margin-bottom: 4px;
    }
    
    .icon-upload-text {
      font-size: 12px;
      text-align: center;
      line-height: 1.2;
    }
  }

  .icon-tips {
    flex: 1;
    color: #909399;
    font-size: 12px;
    line-height: 1.5;
    
    p {
      margin: 0 0 4px 0;
    }
  }
}

.link-preview {
  margin-top: 8px;
  padding: 8px 12px;
  background: #f5f7fa;
  border-radius: 4px;
  font-size: 12px;
  
  .link-label {
    color: #909399;
    margin-right: 8px;
  }
  
  .link-url {
    color: #409eff;
    text-decoration: none;
    word-break: break-all;
    
    &:hover {
      text-decoration: underline;
    }
  }
}

.status-tips,
.sort-tips {
  margin-top: 4px;
  color: #909399;
  font-size: 12px;
  line-height: 1.4;
}

.preview-section {
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
  
  h4 {
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: 600;
    color: #303133;
  }
}

.diamond-preview {
  display: flex;
  justify-content: center;
  
  .preview-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 16px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    background: #fafafa;
    width: 100px;
    
    .preview-icon {
      width: 48px;
      height: 48px;
      margin-bottom: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;
      background: white;
      
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 4px;
      }
      
      .placeholder-icon {
        font-size: 24px;
        color: #c0c4cc;
      }
    }
    
    .preview-name {
      font-size: 12px;
      text-align: center;
      color: #606266;
      line-height: 1.2;
      word-break: break-all;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

// 响应式设计
@media (max-width: 768px) {
  .icon-upload-container {
    flex-direction: column;
    
    .icon-tips {
      margin-top: 8px;
    }
  }
}

// 图标类型相关样式
.icon-input-group {
  .input-with-upload {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .textarea-with-upload {
    position: relative;

    .upload-btn-overlay {
      position: absolute;
      top: 8px;
      right: 8px;
      z-index: 10;
    }
  }

  .form-help {
    color: #909399;
    font-size: 12px;
    margin-top: 4px;
    line-height: 1.4;
  }
}

.icon-preview-container {
  margin-top: 16px;
  padding: 12px;
  border: 1px dashed #dcdfe6;
  border-radius: 6px;
  background-color: #fafafa;

  .preview-label {
    font-size: 14px;
    color: #606266;
    margin-bottom: 8px;
  }

  .preview-content {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 40px;

    .preview-icon {
      font-size: 24px;

      img {
        width: 24px;
        height: 24px;
        object-fit: contain;
      }
    }

    .preview-placeholder {
      color: #c0c4cc;
      font-size: 12px;
    }
  }
}
</style>