import { ref, reactive } from 'vue'
import type {
  DiamondPosition,
  CreateDiamondPositionRequest,
  UpdateDiamondPositionRequest,
  UpdateSortOrderRequest,
  DiamondPositionQuery,
  PaginatedResponse,
  ApiResponse,
  IconType,
  ImageUploadResponse,
  SvgUploadResponse,
  FileUploadParams,
  IconValidationRule
} from '../types/diamond-position'

/**
 * 金刚位管理组合式函数
 */
export function useDiamondPosition() {
  // 响应式数据
  const items = ref<DiamondPosition[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 模拟API基础URL
  const API_BASE_URL = '/api/diamond-positions'

  /**
   * 获取金刚位列表
   */
  const fetchItems = async (query?: DiamondPositionQuery): Promise<void> => {
    loading.value = true
    error.value = null

    try {
      // 构建查询参数
      const params = new URLSearchParams()
      if (query?.keyword) params.append('keyword', query.keyword)
      if (query?.status !== undefined) params.append('status', String(query.status))
      if (query?.iconType) params.append('iconType', query.iconType)
      if (query?.page) params.append('page', String(query.page))
      if (query?.pageSize) params.append('pageSize', String(query.pageSize))

      const url = `${API_BASE_URL}${params.toString() ? '?' + params.toString() : ''}`
      
      // 模拟API调用
      const response = await mockApiCall<PaginatedResponse<DiamondPosition>>('GET', url)
      
      if (response.success) {
        items.value = response.data.data
      } else {
        throw new Error(response.message)
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取数据失败'
      console.error('获取金刚位列表失败:', err)
    } finally {
      loading.value = false
    }
  }

  /**
   * 创建金刚位
   */
  const createItem = async (data: Partial<DiamondPosition>): Promise<void> => {
    loading.value = true
    error.value = null

    try {
      const response = await mockApiCall<DiamondPosition>('POST', API_BASE_URL, data)
      
      if (response.success) {
        // 添加到本地列表
        items.value.push(response.data)
      } else {
        throw new Error(response.message)
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '创建失败'
      console.error('创建金刚位失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 更新金刚位
   */
  const updateItem = async (id: number, data: Partial<DiamondPosition>): Promise<void> => {
    loading.value = true
    error.value = null

    try {
      const response = await mockApiCall<DiamondPosition>('PUT', `${API_BASE_URL}/${id}`, data)
      
      if (response.success) {
        // 更新本地列表
        const index = items.value.findIndex(item => item.id === id)
        if (index !== -1) {
          items.value[index] = { ...items.value[index], ...response.data }
        }
      } else {
        throw new Error(response.message)
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '更新失败'
      console.error('更新金刚位失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 删除金刚位
   */
  const deleteItem = async (id: number): Promise<void> => {
    loading.value = true
    error.value = null

    try {
      const response = await mockApiCall<void>('DELETE', `${API_BASE_URL}/${id}`)
      
      if (response.success) {
        // 从本地列表移除
        const index = items.value.findIndex(item => item.id === id)
        if (index !== -1) {
          items.value.splice(index, 1)
        }
      } else {
        throw new Error(response.message)
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '删除失败'
      console.error('删除金刚位失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 切换启用状态
   */
  const toggleStatus = async (id: number, isEnabled: boolean): Promise<void> => {
    loading.value = true
    error.value = null

    try {
      const response = await mockApiCall<DiamondPosition>('PATCH', `${API_BASE_URL}/${id}/status`, {
        isEnabled
      })
      
      if (response.success) {
        // 更新本地列表
        const index = items.value.findIndex(item => item.id === id)
        if (index !== -1) {
          items.value[index].isEnabled = isEnabled
        }
      } else {
        throw new Error(response.message)
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '状态切换失败'
      console.error('切换状态失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 更新排序
   */
  const updateSortOrder = async (orderData: UpdateSortOrderRequest[]): Promise<void> => {
    loading.value = true
    error.value = null

    try {
      const response = await mockApiCall<void>('PUT', `${API_BASE_URL}/sort-order`, {
        items: orderData
      })
      
      if (response.success) {
        // 更新本地排序
        orderData.forEach(({ id, sortOrder }) => {
          const item = items.value.find(item => item.id === id)
          if (item) {
            item.sortOrder = sortOrder
          }
        })
      } else {
        throw new Error(response.message)
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '排序更新失败'
      console.error('更新排序失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 批量删除
   */
  const batchDelete = async (ids: number[]): Promise<void> => {
    loading.value = true
    error.value = null

    try {
      const response = await mockApiCall<void>('DELETE', `${API_BASE_URL}/batch`, {
        ids
      })
      
      if (response.success) {
        // 从本地列表移除
        items.value = items.value.filter(item => !ids.includes(item.id!))
      } else {
        throw new Error(response.message)
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '批量删除失败'
      console.error('批量删除失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 批量更新状态
   */
  const batchUpdateStatus = async (ids: number[], isEnabled: boolean): Promise<void> => {
    loading.value = true
    error.value = null

    try {
      const response = await mockApiCall<void>('PATCH', `${API_BASE_URL}/batch/status`, {
        ids,
        isEnabled
      })
      
      if (response.success) {
        // 更新本地状态
        items.value.forEach(item => {
          if (ids.includes(item.id!)) {
            item.isEnabled = isEnabled
          }
        })
      } else {
        throw new Error(response.message)
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '批量状态更新失败'
      console.error('批量状态更新失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  return {
    // 状态
    items,
    loading,
    error,
    
    // 方法
    fetchItems,
    createItem,
    updateItem,
    deleteItem,
    toggleStatus,
    updateSortOrder,
    batchDelete,
    batchUpdateStatus
  }
}

/**
 * 模拟API调用函数
 */
async function mockApiCall<T>(
  method: string,
  url: string,
  data?: any
): Promise<ApiResponse<T>> {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 300 + Math.random() * 700))

  // 模拟数据
  const mockData: DiamondPosition[] = [
    {
      id: 1,
      name: '扫一扫',
      iconUrl: 'https://via.placeholder.com/64x64/4CAF50/white?text=扫',
      linkUrl: '/scan',
      sortOrder: 1,
      isEnabled: true,
      description: '扫码功能',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    },
    {
      id: 2,
      name: '付款码',
      iconUrl: 'https://via.placeholder.com/64x64/2196F3/white?text=付',
      linkUrl: '/payment',
      sortOrder: 2,
      isEnabled: true,
      description: '付款码功能',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    },
    {
      id: 3,
      name: '充值',
      iconUrl: 'https://via.placeholder.com/64x64/FF9800/white?text=充',
      linkUrl: '/recharge',
      sortOrder: 3,
      isEnabled: true,
      description: '账户充值',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    },
    {
      id: 4,
      name: '转账',
      iconUrl: 'https://via.placeholder.com/64x64/9C27B0/white?text=转',
      linkUrl: '/transfer',
      sortOrder: 4,
      isEnabled: false,
      description: '转账功能',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    },
    {
      id: 5,
      name: '理财',
      iconUrl: 'https://via.placeholder.com/64x64/F44336/white?text=理',
      linkUrl: '/finance',
      sortOrder: 5,
      isEnabled: true,
      description: '理财产品',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    },
    {
      id: 6,
      name: '信用卡',
      iconUrl: 'https://via.placeholder.com/64x64/607D8B/white?text=信',
      linkUrl: '/credit-card',
      sortOrder: 6,
      isEnabled: true,
      description: '信用卡服务',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    }
  ]

  // 根据不同的请求返回不同的模拟数据
  if (method === 'GET' && url.includes('/api/diamond-positions')) {
    return {
      success: true,
      data: {
        data: mockData,
        total: mockData.length,
        page: 1,
        pageSize: 20,
        totalPages: 1
      } as T,
      message: '获取成功'
    }
  }

  if (method === 'POST') {
    const newItem: DiamondPosition = {
      id: Date.now(),
      ...data,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    return {
      success: true,
      data: newItem as T,
      message: '创建成功'
    }
  }

  if (method === 'PUT' || method === 'PATCH') {
    return {
      success: true,
      data: { ...data, updatedAt: new Date().toISOString() } as T,
      message: '更新成功'
    }
  }

  if (method === 'DELETE') {
    return {
      success: true,
      data: null as T,
      message: '删除成功'
    }
  }

  return {
    success: true,
    data: null as T,
    message: '操作成功'
  }
}

/**
 * 图标处理组合式函数
 */
export function useIconHandler() {
  // 图标验证规则
  const iconValidationRules: IconValidationRule[] = [
    {
      type: 'url',
      pattern: /^(https?:\/\/|\/)/,
      message: 'URL类型的图标必须以 http://、https:// 或 / 开头'
    },
    {
      type: 'svg',
      pattern: /^<svg.*<\/svg>$/s,
      message: 'SVG类型的图标必须以 <svg 开头，以 </svg> 结尾'
    },
    {
      type: 'base64',
      pattern: /^data:image\//,
      message: 'Base64类型的图标必须以 data:image/ 开头'
    },
    {
      type: 'emoji',
      maxLength: 10,
      message: '表情符号类型的图标长度不能超过10个字符'
    }
  ]

  /**
   * 验证图标内容
   */
  const validateIcon = (iconType: IconType, iconContent: string): { valid: boolean; message?: string } => {
    const rule = iconValidationRules.find(r => r.type === iconType)
    if (!rule) {
      return { valid: false, message: '不支持的图标类型' }
    }

    if (rule.pattern && !rule.pattern.test(iconContent)) {
      return { valid: false, message: rule.message }
    }

    if (rule.maxLength && iconContent.length > rule.maxLength) {
      return { valid: false, message: rule.message }
    }

    return { valid: true }
  }

  /**
   * 自动检测图标类型
   */
  const detectIconType = (iconContent: string): IconType => {
    if (/^(https?:\/\/|\/)/.test(iconContent)) {
      return 'url'
    } else if (/^<svg.*<\/svg>$/s.test(iconContent.trim())) {
      return 'svg'
    } else if (/^data:image\//.test(iconContent)) {
      return 'base64'
    } else {
      return 'emoji'
    }
  }

  /**
   * 渲染图标组件属性
   */
  const getIconProps = (icon: string, iconType: IconType) => {
    switch (iconType) {
      case 'url':
      case 'base64':
        return {
          component: 'img',
          props: { src: icon, alt: '图标' }
        }
      case 'svg':
        return {
          component: 'div',
          props: { innerHTML: icon }
        }
      case 'emoji':
        return {
          component: 'span',
          props: { textContent: icon }
        }
      default:
        return {
          component: 'span',
          props: { textContent: icon }
        }
    }
  }

  return {
    iconValidationRules,
    validateIcon,
    detectIconType,
    getIconProps
  }
}

/**
 * 文件上传组合式函数
 */
export function useFileUpload() {
  const uploading = ref(false)
  const uploadProgress = ref(0)

  /**
   * 上传图片文件
   */
  const uploadImage = async (params: FileUploadParams): Promise<ImageUploadResponse> => {
    uploading.value = true
    uploadProgress.value = 0

    try {
      // 验证文件类型
      if (!params.file.type.startsWith('image/')) {
        throw new Error('请选择图片文件')
      }

      // 验证文件大小（Base64类型限制2MB，其他类型限制5MB）
      const maxSize = params.type === 'base64' ? 2 * 1024 * 1024 : 5 * 1024 * 1024
      if (params.file.size > maxSize) {
        const maxSizeMB = maxSize / (1024 * 1024)
        throw new Error(`文件大小不能超过${maxSizeMB}MB`)
      }

      // 模拟上传进度
      const progressInterval = setInterval(() => {
        uploadProgress.value += 10
        if (uploadProgress.value >= 90) {
          clearInterval(progressInterval)
        }
      }, 100)

      // 根据类型处理文件
      let result: ImageUploadResponse

      if (params.type === 'base64') {
        // 转换为Base64
        const base64 = await fileToBase64(params.file)
        result = {
          base64,
          originalName: params.file.name,
          size: params.file.size,
          mimeType: params.file.type
        }
      } else {
        // 模拟上传到服务器
        await new Promise(resolve => setTimeout(resolve, 1000))
        const url = `https://example.com/uploads/${Date.now()}_${params.file.name}`
        result = {
          url,
          originalName: params.file.name,
          size: params.file.size,
          mimeType: params.file.type
        }
      }

      uploadProgress.value = 100
      return result

    } catch (error) {
      throw error
    } finally {
      uploading.value = false
      setTimeout(() => {
        uploadProgress.value = 0
      }, 1000)
    }
  }

  /**
   * 上传SVG文件
   */
  const uploadSvg = async (file: File): Promise<SvgUploadResponse> => {
    uploading.value = true

    try {
      // 验证文件类型
      if (file.type !== 'image/svg+xml' && !file.name.endsWith('.svg')) {
        throw new Error('请选择SVG文件')
      }

      // 验证文件大小
      if (file.size > 1024 * 1024) {
        throw new Error('SVG文件大小不能超过1MB')
      }

      // 读取文件内容
      const svgContent = await fileToText(file)

      // 验证SVG格式
      if (!svgContent.trim().startsWith('<svg')) {
        throw new Error('无效的SVG文件')
      }

      return {
        svgContent,
        originalName: file.name,
        size: file.size
      }

    } catch (error) {
      throw error
    } finally {
      uploading.value = false
    }
  }

  /**
   * 文件转Base64
   */
  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => resolve(reader.result as string)
      reader.onerror = () => reject(new Error('文件读取失败'))
      reader.readAsDataURL(file)
    })
  }

  /**
   * 文件转文本
   */
  const fileToText = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => resolve(reader.result as string)
      reader.onerror = () => reject(new Error('文件读取失败'))
      reader.readAsText(file)
    })
  }

  return {
    uploading,
    uploadProgress,
    uploadImage,
    uploadSvg,
    fileToBase64,
    fileToText
  }
}