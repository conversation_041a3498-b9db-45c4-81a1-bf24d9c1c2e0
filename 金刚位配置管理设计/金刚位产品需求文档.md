# 金刚位配置管理产品需求文档

## 1. 产品概述

### 1.1 产品背景
金刚位是移动应用首页的核心功能入口，为用户提供快速访问各种服务的便捷方式。随着业务发展，需要一个灵活、易用的配置管理系统来动态调整金刚位的显示内容和排序。

### 1.2 产品目标
- 提供直观的金刚位配置管理界面
- 支持拖拽排序，提升操作体验
- 实现多设备响应式预览
- 简化配置流程，降低运营成本

### 1.3 目标用户
- **主要用户**：产品运营人员、配置管理员
- **次要用户**：产品经理、开发人员

## 2. 功能需求

### 2.1 核心功能

#### 2.1.1 金刚位网格配置
- **功能描述**：以网格形式展示当前金刚位配置
- **交互方式**：
  - 支持拖拽排序调整显示顺序
  - 点击金刚位进行编辑
  - 实时显示排序变化
- **视觉效果**：
  - 网格布局，清晰展示每个金刚位
  - 拖拽时提供视觉反馈
  - 支持启用/禁用状态显示

#### 2.1.2 金刚位列表管理
- **功能描述**：以表格形式管理金刚位详细信息
- **包含字段**：
  - 金刚位名称
  - 图标（支持多种类型显示）
  - 图标类型（URL/SVG/Base64/表情符号）
  - 跳转链接
  - 显示状态
  - 排序顺序
  - 操作按钮（编辑、删除、启用/禁用）
- **操作功能**：
  - 搜索过滤
  - 批量操作
  - 分页显示

#### 2.1.3 新增/编辑金刚位
- **功能描述**：通过模态对话框进行金刚位的新增和编辑
- **表单字段**：
  - 金刚位名称（必填）
  - 图标类型选择（必填）：
    - URL：网络链接或本地路径
    - SVG：SVG矢量图代码
    - Base64：Base64编码图片
    - 表情符号：Unicode表情符号
  - 图标内容（必填）：
    - URL类型：输入框输入链接地址 + 图片上传功能
    - SVG类型：文本域输入SVG代码 + SVG文件上传功能
    - Base64类型：文本域粘贴Base64编码 + 图片上传转换功能
    - 表情符号类型：表情符号选择器或直接输入
  - 跳转链接（必填）
  - 显示状态（启用/禁用）
  - 排序权重
  - 描述信息（可选）
- **验证规则**：
  - 名称不能为空且不能重复
  - 链接格式验证
  - 图标类型和内容匹配性验证：
    - URL类型：验证链接格式
    - SVG类型：验证SVG代码格式
    - Base64类型：验证Base64编码格式
    - 表情符号类型：验证字符长度限制
- **交互体验**：
  - 图标类型切换时，动态显示对应的输入组件
  - 实时预览图标显示效果
  - 提供图标类型使用示例和帮助说明
- **文件上传功能**：
  - URL类型：支持上传图片文件，自动生成URL地址
  - SVG类型：支持上传SVG文件，自动读取SVG代码
  - Base64类型：支持上传图片文件，自动转换为Base64编码
  - 上传进度提示和结果反馈
  - 文件类型和大小验证

#### 2.1.4 图标文件上传功能
- **功能描述**：支持本地文件上传，自动转换为对应的图标格式
- **上传类型支持**：
  - **图片转URL**：
    - 支持格式：PNG、JPG、JPEG、GIF、WebP
    - 文件大小限制：5MB以内
    - 上传后生成可访问的URL地址
    - 自动压缩和优化图片
  - **SVG文件上传**：
    - 支持格式：SVG文件
    - 文件大小限制：1MB以内
    - 自动读取SVG代码内容
    - 验证SVG格式有效性
  - **图片转Base64**：
    - 支持格式：PNG、JPG、JPEG、GIF
    - 文件大小限制：2MB以内
    - 自动转换为Base64编码
    - 实时显示转换进度
- **上传体验**：
  - 拖拽上传支持
  - 上传进度显示
  - 成功/失败状态反馈
  - 文件预览功能
  - 错误提示和重试机制

#### 2.1.5 多设备预览
- **功能描述**：实时预览金刚位在不同设备上的显示效果
- **支持设备**：
  - 手机端（2列布局）
  - 平板端（4列布局）
  - 桌面端（6列布局）
- **预览特性**：
  - 实时同步配置变化
  - 响应式布局展示
  - 设备切换动画

### 2.2 辅助功能

#### 2.2.1 搜索和筛选
- 按名称搜索金刚位
- 按状态筛选（全部/启用/禁用）
- 搜索结果高亮显示

#### 2.2.2 批量操作
- 批量启用/禁用
- 批量删除
- 批量导出配置

#### 2.2.3 操作记录
- 记录配置变更历史
- 支持操作回滚
- 变更日志查看

## 3. 非功能需求

### 3.1 性能要求
- 页面加载时间 < 2秒
- 拖拽操作响应时间 < 100ms
- 支持同时管理100+金刚位

### 3.2 兼容性要求
- 支持主流浏览器（Chrome、Firefox、Safari、Edge）
- 响应式设计，适配移动端和桌面端
- 支持触摸设备操作

### 3.3 易用性要求
- 界面简洁直观，符合用户操作习惯
- 提供操作提示和帮助信息
- 支持键盘快捷键操作

### 3.4 安全性要求
- 用户权限验证
- 操作日志记录
- 数据备份和恢复

## 4. 用户体验设计

### 4.1 界面布局
- **顶部工具栏**：搜索、新增、批量操作按钮
- **主要内容区**：
  - 左侧：金刚位网格配置区
  - 右侧：多设备预览区
- **底部区域**：金刚位列表表格

### 4.2 交互设计
- **拖拽排序**：
  - 拖拽开始时显示提示信息
  - 拖拽过程中高亮目标位置
  - 拖拽完成后显示成功提示
- **模态对话框**：
  - 表单验证实时反馈
  - 保存成功后自动关闭
  - 支持ESC键关闭

### 4.3 视觉设计
- 采用现代化扁平设计风格
- 使用一致的颜色体系和图标
- 提供清晰的状态反馈

## 5. 技术架构

### 5.1 前端技术栈
- **框架**：Vue 3 + TypeScript
- **UI组件库**：Element Plus
- **构建工具**：Vite
- **状态管理**：Pinia
- **路由管理**：Vue Router

### 5.2 后端技术栈
- **框架**：Spring Boot
- **数据库**：MySQL
- **API设计**：RESTful API
- **文档工具**：Swagger

### 5.3 数据库设计
```sql
CREATE TABLE diamond_position (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL COMMENT '金刚位名称',
    icon_url VARCHAR(255) NOT NULL COMMENT '图标URL',
    link_url VARCHAR(255) NOT NULL COMMENT '跳转链接',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    is_enabled TINYINT(1) DEFAULT 1 COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## 6. 开发计划

### 6.1 开发阶段
- **阶段一**：基础框架搭建和数据库设计
- **阶段二**：核心功能开发（CRUD操作）
- **阶段三**：拖拽排序功能实现
- **阶段四**：多设备预览功能
- **阶段五**：测试和优化

### 6.2 里程碑
- **里程碑1**：完成产品原型设计
- **里程碑2**：完成后端API开发
- **里程碑3**：完成前端核心功能
- **里程碑4**：完成集成测试
- **里程碑5**：产品上线

## 7. 验收标准

### 7.1 功能验收
- [ ] 金刚位网格配置功能正常
- [ ] 拖拽排序功能流畅
- [ ] 新增/编辑功能完整
- [ ] 多设备预览准确
- [ ] 搜索筛选功能有效
- [ ] 批量操作功能正常

### 7.2 性能验收
- [ ] 页面加载时间符合要求
- [ ] 拖拽操作响应及时
- [ ] 大量数据处理正常

### 7.3 兼容性验收
- [ ] 主流浏览器兼容
- [ ] 移动端适配良好
- [ ] 触摸操作支持

## 8. 风险评估

### 8.1 技术风险
- **拖拽功能兼容性**：不同浏览器对HTML5拖拽API支持差异
- **性能风险**：大量金刚位数据的渲染性能

### 8.2 业务风险
- **用户接受度**：新界面的用户学习成本
- **数据迁移**：现有配置数据的迁移风险

### 8.3 风险应对
- 提供降级方案和兼容性处理
- 进行充分的用户测试和反馈收集
- 制定详细的数据迁移计划

## 9. 后续规划

### 9.1 功能扩展
- 支持金刚位分组管理
- 添加A/B测试功能
- 集成数据分析和统计

### 9.2 技术优化
- 引入虚拟滚动优化性能
- 添加离线缓存支持
- 实现实时协作编辑

---

**文档版本**：v1.0  
**创建日期**：2025年1月23日  
**最后更新**：2025年1月23日  
**文档状态**：待评审