# 金刚位图标类型设计文档

## 概述
为了支持多种图标格式，金刚位配置增加了图标类型字段，前端可以根据类型判断加载方式。

## 图标类型定义

### 1. URL类型 (url)
- **描述**: 图标为网络链接或本地路径
- **格式**: 
  - `https://example.com/icon.png`
  - `http://example.com/icon.svg`
  - `/static/icons/icon.png`
- **前端处理**: 直接作为img标签的src属性

### 2. SVG类型 (svg)
- **描述**: 图标为SVG代码
- **格式**: 
  ```svg
  <svg viewBox="0 0 24 24">
    <path d="M12 2L2 7v10c0 5.55 3.84 10 9 10s9-4.45 9-10V7l-10-5z"/>
  </svg>
  ```
- **前端处理**: 直接插入DOM或使用dangerouslySetInnerHTML

### 3. Base64类型 (base64)
- **描述**: 图标为Base64编码的图片
- **格式**: `data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==`
- **前端处理**: 直接作为img标签的src属性

### 4. 表情符号类型 (emoji)
- **描述**: 图标为Unicode表情符号
- **格式**: `💰`, `🔧`, `💳`
- **前端处理**: 直接显示文本，可设置字体大小

## 数据库设计

### 字段定义
```sql
`icon` text DEFAULT NULL COMMENT '图标内容（URL、SVG代码、Base64等）',
`icon_type` varchar(20) DEFAULT 'url' COMMENT '图标类型：url-链接地址，svg-SVG代码，base64-Base64编码，emoji-表情符号',
```

### 索引
```sql
KEY `idx_icon_type` (`icon_type`)
```

## 后端验证规则

### 1. 图标类型验证
- 只允许: `url`, `svg`, `base64`, `emoji`
- 必填字段，默认值为 `url`

### 2. 图标内容验证
- **URL类型**: 必须以 `http://`, `https://` 或 `/` 开头
- **SVG类型**: 必须以 `<svg` 开头，以 `</svg>` 结尾
- **Base64类型**: 必须以 `data:image/` 开头
- **表情符号类型**: 长度不超过10个字符

### 3. 自动检测
如果未指定图标类型，系统会根据图标内容自动检测：
- 以 `http://`, `https://`, `/` 开头 → `url`
- 以 `<svg` 开头且以 `</svg>` 结尾 → `svg`
- 以 `data:image/` 开头 → `base64`
- 其他情况 → `emoji`

## 前端使用示例

### React组件示例
```jsx
const IconRenderer = ({ icon, iconType }) => {
  switch (iconType) {
    case 'url':
      return <img src={icon} alt="icon" />;
    case 'svg':
      return <div dangerouslySetInnerHTML={{ __html: icon }} />;
    case 'base64':
      return <img src={icon} alt="icon" />;
    case 'emoji':
      return <span className="emoji-icon">{icon}</span>;
    default:
      return <img src={icon} alt="icon" />;
  }
};
```

### Vue组件示例
```vue
<template>
  <div>
    <img v-if="iconType === 'url' || iconType === 'base64'" :src="icon" alt="icon" />
    <div v-else-if="iconType === 'svg'" v-html="icon"></div>
    <span v-else-if="iconType === 'emoji'" class="emoji-icon">{{ icon }}</span>
  </div>
</template>
```

## API响应示例

```json
{
  "id": 1,
  "name": "扫一扫",
  "icon": "https://example.com/icons/scan.png",
  "iconType": "url",
  "url": "/scan",
  "sortOrder": 1,
  "status": 1,
  "description": "扫码功能"
}
```

## 兼容性说明

### 向后兼容
- 现有数据如果没有 `icon_type` 字段，默认为 `url` 类型
- 系统会自动检测并设置合适的图标类型

### 迁移建议
1. 为现有数据添加 `icon_type` 字段，默认值为 `url`
2. 运行数据迁移脚本，自动检测并设置正确的图标类型
3. 更新前端代码，支持多种图标类型的渲染

## 性能考虑

### 1. 图标大小限制
- URL类型: 无特殊限制
- SVG类型: 建议不超过10KB
- Base64类型: 建议不超过100KB
- 表情符号类型: 不超过10个字符

### 2. 缓存策略
- URL类型: 利用浏览器缓存和CDN
- SVG类型: 可以内联，减少HTTP请求
- Base64类型: 内联，但会增加数据传输量
- 表情符号类型: 无需缓存，直接显示

## 安全考虑

### 1. XSS防护
- SVG内容需要进行安全过滤，防止恶意脚本
- 建议使用DOMPurify等库进行SVG内容清理

### 2. 内容验证
- 严格验证图标类型和内容的匹配性
- 对Base64内容进行格式验证
- 限制SVG内容的复杂度
