package com.oto.web.domain.dto;

import com.oto.constantandenums.enums.PlatFormTypes;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @createTime 2025/5/23 14:18
 * @description
 */
@Data
public class CaptchaDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 4130282271055887178L;


    private String phonenumber;
    private String captchaType;
    private String captchaVerification;
    private PlatFormTypes platformType;
}
