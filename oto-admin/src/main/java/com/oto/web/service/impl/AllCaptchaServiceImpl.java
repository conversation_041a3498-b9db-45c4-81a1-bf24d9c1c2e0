package com.oto.web.service.impl;

import cn.hutool.captcha.AbstractCaptcha;
import cn.hutool.captcha.generator.CodeGenerator;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.anji.captcha.model.common.ResponseModel;
import com.anji.captcha.model.vo.CaptchaVO;
import com.anji.captcha.service.CaptchaService;
import com.oto.common.core.utils.ServletUtils;
import com.oto.manage.otoconfig.service.OtoConfigService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.oto.common.core.constant.Constants;
import com.oto.common.core.constant.GlobalConstants;
import com.oto.common.core.exception.ServiceException;
import com.oto.common.core.utils.SpringUtils;
import com.oto.common.core.utils.StringUtils;
import com.oto.common.core.utils.reflect.ReflectUtils;
import com.oto.common.encrypt.utils.HmacPhoneUtils;
import com.oto.common.mail.config.properties.MailProperties;
import com.oto.common.mail.utils.MailUtils;
import com.oto.common.ratelimiter.annotation.RateLimiter;
import com.oto.common.ratelimiter.enums.LimitType;
import com.oto.common.redis.utils.RedisUtils;
import com.oto.common.web.config.properties.CaptchaProperties;
import com.oto.common.web.enums.CaptchaType;
import org.dromara.sms4j.api.SmsBlend;
import org.dromara.sms4j.api.entity.SmsResponse;
import org.dromara.sms4j.core.factory.SmsFactory;
import com.oto.web.domain.dto.CaptchaDTO;
import com.oto.web.domain.vo.CaptchaVo;
import com.oto.web.service.AllCaptchaService;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.LinkedHashMap;

/**
 * 统一验证码服务实现
 *
 * <AUTHOR>
 * @createTime 2025/7/3 15:56
 * @description
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AllCaptchaServiceImpl implements AllCaptchaService {

    private final CaptchaService captchaService;
    private final HmacPhoneUtils hmacPhoneUtils;
    private final MailProperties mailProperties;
    private final CaptchaProperties captchaProperties;
    private final OtoConfigService otoConfigService;

    /**
     * 生成并缓存短信验证码
     * @param captchaDTO 包含手机号、滑块验证码等信息
     * @return 错误信息，如果成功则返回null
     */
    @Override
    @RateLimiter(key = "#captchaDTO.phonenumber", time = 60, count = 1, dayLimitCount = 5, encrypt = true)
    // todo: 暂时关闭  后面限流可能需要调整 因为它有client参数 需要搞明白
    public String generateAndCacheSmsCode(CaptchaDTO captchaDTO) {
        // 判断是否需要校验滑块验证码

        // todo: 接口防刷
        String hashPhone = hmacPhoneUtils.hashPhone(captchaDTO.getPhonenumber());
        String key = GlobalConstants.MEMBER_CAPTCHA_CODE_KEY + hashPhone;
        String code = RandomUtil.randomNumbers(6);
        RedisUtils.setCacheObject(key, code, Duration.ofMinutes(Constants.CAPTCHA_EXPIRATION));
        // 短信发送逻辑
        // todo: 上线前接入短信服务
        //        String templateId = "";
        //        LinkedHashMap<String, String> map = new LinkedHashMap<>(1);
        //        map.put("code", code);
        //        SmsBlend smsBlend = SmsFactory.getSmsBlend("config1");
        //        SmsResponse smsResponse = smsBlend.sendMessage(phonenumber, templateId, map);
        //        if (!smsResponse.isSuccess()) {
        //            log.error("验证码短信发送异常 => {}", smsResponse);
        //            return smsResponse.getData().toString();
        //        }
        log.info("登录注册接口验证码短信发送成功 , 手机号：{},验证码: {}", hashPhone, code);
        return null; // null 表示成功
    }

    /**
     * 校验短信验证码
     * @param phonenumber 手机号
     * @param code 验证码
     * @return 校验结果，true为通过，false为失败
     */
    @Override
    public boolean verifySmsCode(String phonenumber, String code) {
        // 手机号加密处理，保证安全
        String hashPhone = hmacPhoneUtils.hashPhone(phonenumber);
        String key = GlobalConstants.MEMBER_CAPTCHA_CODE_KEY + hashPhone;
        String cacheCode = RedisUtils.getCacheObject(key);
        if (cacheCode != null && cacheCode.equals(code)) {
            // 校验通过后可选择删除验证码，防止重复使用
            RedisUtils.deleteObject(key);
            return true;
        }
        return false;
    }

    /**
     * 生成并缓存邮箱验证码
     * @param email 邮箱
     */
    @Override
    public void generateAndCacheEmailCode(String email) {
        if (!mailProperties.getEnabled()) {
            throw new ServiceException("当前系统没有开启邮箱功能！");
        }
        String key = GlobalConstants.CAPTCHA_CODE_KEY + email;
        String code = RandomUtil.randomNumbers(4);
        RedisUtils.setCacheObject(key, code, Duration.ofMinutes(Constants.CAPTCHA_EXPIRATION));
        try {
            MailUtils.sendText(email, "登录验证码", "您本次验证码为：" + code + "，有效性为" + Constants.CAPTCHA_EXPIRATION + "分钟，请尽快填写。");
        } catch (Exception e) {
            log.error("验证码短信发送异常 => {}", e.getMessage());
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 校验邮箱验证码
     * @param email 邮箱
     * @param code 验证码
     * @return 校验结果
     */
    @Override
    public boolean verifyEmailCode(String email, String code) {
        String key = GlobalConstants.CAPTCHA_CODE_KEY + email;
        String cacheCode = RedisUtils.getCacheObject(key);
        if (cacheCode != null && cacheCode.equals(code)) {
            RedisUtils.deleteObject(key);
            return true;
        }
        return false;
    }

    /**
     * 生成aj滑块验证码
     * @param data 滑块验证码请求参数
     * @param request HttpServletRequest
     * @return 滑块验证码图片等信息
     */
    @Override
    public ResponseModel getAjCaptcha(CaptchaVO data, HttpServletRequest request) {
        assert request.getRemoteHost() != null;
        data.setBrowserInfo(getRemoteId(request));
        return captchaService.get(data);
    }

    /**
     * 校验aj滑块验证码
     * @param data 滑块验证码校验参数
     * @param request HttpServletRequest
     * @return 校验结果
     */
    @Override
    public ResponseModel checkAjCaptcha(CaptchaVO data, HttpServletRequest request) {
        data.setBrowserInfo(getRemoteId(request));
        return captchaService.check(data);
    }

    /**
     * 二次校验aj滑块验证码
     * @param data 滑块验证码校验参数
     * @return 校验结果
     */
    @Override
    public ResponseModel verifyAjCaptcha(CaptchaVO data) {
        ResponseModel response = captchaService.verification(data);
        log.info("AJ滑块验证码二次校验结果 => 参数: {}, 校验结果: {}", JSON.toJSONString(data), response.isSuccess() ? "成功" : "失败");
        return response;
    }

    /**
     * 生成图形验证码
     * @return 验证码信息（uuid, img）
     */
    @Override
    @RateLimiter(time = 60, count = 10, limitType = LimitType.IP)
    public CaptchaVo generateCaptchaCode() {
        boolean captchaEnabled = captchaProperties.getEnable();
        if (!captchaEnabled) {
            CaptchaVo captchaVo = new CaptchaVo();
            captchaVo.setCaptchaEnabled(false);
            return captchaVo;
        }

        // 保存验证码信息
        String uuid = IdUtil.simpleUUID();
        String verifyKey = GlobalConstants.CAPTCHA_CODE_KEY + uuid;
        // 生成验证码
        CaptchaType captchaType = captchaProperties.getType();
        boolean isMath = CaptchaType.MATH == captchaType;
        Integer length = isMath ? captchaProperties.getNumberLength() : captchaProperties.getCharLength();
        CodeGenerator codeGenerator = ReflectUtils.newInstance(captchaType.getClazz(), length);
        AbstractCaptcha captcha = SpringUtils.getBean(captchaProperties.getCategory().getClazz());
        captcha.setGenerator(codeGenerator);
        captcha.createCode();
        // 如果是数学验证码，使用SpEL表达式处理验证码结果
        String code = captcha.getCode();
        if (isMath) {
            ExpressionParser parser = new SpelExpressionParser();
            Expression exp = parser.parseExpression(StringUtils.remove(code, "="));
            code = exp.getValue(String.class);
        }
        RedisUtils.setCacheObject(verifyKey, code, Duration.ofMinutes(Constants.CAPTCHA_EXPIRATION));
        CaptchaVo captchaVo = new CaptchaVo();
        captchaVo.setUuid(uuid);
        captchaVo.setImg(captcha.getImageBase64());
        return captchaVo;
    }

    /**
     * 校验图形验证码
     * @param uuid 验证码uuid
     * @param code 验证码
     * @return 校验结果
     */
    @Override
    public boolean verifyCaptchaCode(String uuid, String code) {
        String verifyKey = GlobalConstants.CAPTCHA_CODE_KEY + uuid;
        String captchaCode = RedisUtils.getCacheObject(verifyKey);
        RedisUtils.deleteObject(verifyKey);
        return StringUtils.isNotEmpty(captchaCode) && captchaCode.equalsIgnoreCase(code);
    }

    /**
     * 获取远程ID
     * @param request HttpServletRequest
     * @return 远程ID
     */
    @Override
    public String getRemoteId(HttpServletRequest request) {
        String ip = ServletUtils.getClientIP(request);
        String ua = request.getHeader("user-agent");
        if (cn.hutool.core.util.StrUtil.isNotBlank(ip)) {
            return ip + ua;
        }
        return request.getRemoteAddr() + ua;
    }

    /**
     * 生成并缓存简单的短信验证码（不含滑块校验）
     * @param phonenumber 手机号
     */
    @Override
    public void generateAndCacheSimpleSmsCode(String phonenumber) {
        String key = GlobalConstants.CAPTCHA_CODE_KEY + phonenumber;
        String code = RandomUtil.randomNumbers(6);
        RedisUtils.setCacheObject(key, code, Duration.ofMinutes(Constants.CAPTCHA_EXPIRATION));
        // 验证码模板id 自行处理 (查数据库或写死均可)
        String templateId = "";
        LinkedHashMap<String, String> map = new LinkedHashMap<>(1);
        map.put("code", code);
        SmsBlend smsBlend = SmsFactory.getSmsBlend("config1");
        SmsResponse smsResponse = smsBlend.sendMessage(phonenumber, templateId, map);
        if (!smsResponse.isSuccess()) {
            log.error("验证码短信发送异常 => {}", smsResponse);
            throw new ServiceException(smsResponse.getData().toString());
        }
    }
}
