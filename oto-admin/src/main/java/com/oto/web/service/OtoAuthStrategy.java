package com.oto.web.service;


import com.oto.member.domain.response.OtoLoginResponse;
import com.oto.member.domain.vo.MemberLoginRequestVo;
import com.oto.common.core.exception.ServiceException;
import com.oto.common.core.utils.SpringUtils;
import com.oto.system.domain.vo.SysClientVo;

/**
 * 授权策略
 *
 * <AUTHOR>
 */
public interface OtoAuthStrategy {

    String BASE_NAME = "OtoAuthStrategy";

    /**
     * 登录
     *
     * @param body      登录对象
     * @param client    授权管理视图对象
     * @param grantType 授权类型
     * @return 登录验证信息
     */
    static OtoLoginResponse login(MemberLoginRequestVo body, SysClientVo client, String grantType) {
        // 授权类型和客户端id
        String beanName = grantType + BASE_NAME;
        if (!SpringUtils.containsBean(beanName)) {
            throw new ServiceException("授权类型不正确!");
        }
        OtoAuthStrategy instance = SpringUtils.getBean(beanName);
        return instance.login(body, client);
    }

    /**
     * 登录
     *
     * @param body   登录对象
     * @param client 授权管理视图对象
     * @return 登录验证信息
     */
    OtoLoginResponse login(MemberLoginRequestVo body, SysClientVo client);

}
