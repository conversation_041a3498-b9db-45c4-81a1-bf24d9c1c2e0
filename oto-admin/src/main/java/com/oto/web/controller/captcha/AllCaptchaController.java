package com.oto.web.controller.captcha;

import cn.dev33.satoken.annotation.SaIgnore;
import com.anji.captcha.model.common.ResponseModel;
import com.anji.captcha.model.vo.CaptchaVO;
import com.oto.manage.otoconfig.service.OtoConfigService;

import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import com.oto.constantandenums.enums.OtoConfigKeyEnums;
import com.oto.common.core.domain.R;
import com.oto.web.domain.dto.CaptchaDTO;
import com.oto.web.service.AllCaptchaService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@SaIgnore
@RequestMapping("/oto/captcha-api")
@RequiredArgsConstructor
public class AllCaptchaController {

    private final AllCaptchaService allCaptchaService;
    private final OtoConfigService otoConfigService;

    /**
     * 发送短信验证码
     * @param captchaDTO 包含手机号、滑块验证码等信息
     * @return 发送结果
     */

//    @RateLimiter(key = "#captchaDTO.phonenumber", time = 60, count = 1, dayLimitCount = 5, message = "{reteLimit.captcha.sms.code.message}", dayMessage = "{reteLimit.captcha.sms.code.day.message}")
    @PostMapping("/sms/getCode")
    public R<Void> smsCode(@RequestBody CaptchaDTO captchaDTO) {
        Boolean needCaptcha = otoConfigService.getEnableByConfigKey(OtoConfigKeyEnums.OTO_FITHOME_SMSCODE_NEED_CAPTCHA);

        if (needCaptcha) {
            // 二次校验滑块验证码
            CaptchaVO captchaVO = new CaptchaVO();
            captchaVO.setCaptchaVerification(captchaDTO.getCaptchaVerification());
            ResponseModel verification = allCaptchaService.verifyAjCaptcha(captchaVO);
            if (!verification.isSuccess()) {
             return R.fail(verification.getRepMsg());
            }
        }
        String result = allCaptchaService.generateAndCacheSmsCode(captchaDTO);
        if (result != null) {
            return R.fail(result);
        }
        return R.ok();
    }

    /**
     * 生成滑块验证码
     * @param data 滑块验证码请求参数
     * @param request HttpServletRequest
     * @return 滑块验证码图片等信息
     */
    @PostMapping("/captcha/get")
    public R<ResponseModel> getAjCaptcha(@RequestBody CaptchaVO data, HttpServletRequest request) {
        ResponseModel ajCaptcha = allCaptchaService.getAjCaptcha(data, request);
        return R.ok(ajCaptcha);
    }

    /**
     * 校验滑块验证码
     * @param data 滑块验证码校验参数
     * @param request HttpServletRequest
     * @return 校验结果
     */
    @PostMapping("/captcha/check")
    public R<ResponseModel> checkAjCaptcha(@RequestBody CaptchaVO data, HttpServletRequest request) {
        ResponseModel responseModel = allCaptchaService.checkAjCaptcha(data, request);
        return R.ok(responseModel);
    }

    /**
     * 校验滑块验证码（简化版）
     * @param data 滑块验证码校验参数
     * @return 校验结果
     */
    @PostMapping("/captcha/check2")
    public ResponseModel verifyAjCaptcha(@RequestBody CaptchaVO data) {
        return allCaptchaService.verifyAjCaptcha(data);
    }

}

