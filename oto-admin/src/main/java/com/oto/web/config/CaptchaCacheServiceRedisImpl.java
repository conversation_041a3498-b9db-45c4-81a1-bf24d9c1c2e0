package com.oto.web.config;

/**
 * <AUTHOR>
 * @createTime 2025/4/22 14:59
 * @description
 */

import com.anji.captcha.service.CaptchaCacheService;
import lombok.RequiredArgsConstructor;
import com.oto.common.redis.utils.RedisUtils;
import org.redisson.api.RScript;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.time.Duration;
import java.util.Collections;

/**
 * 对于分布式部署的应用，我们建议应用自己实现CaptchaCacheService，比如用Redis，参考service/spring-boot代码示例。
 * 如果应用是单点的，也没有使用redis，那默认使用内存。
 * 内存缓存只适合单节点部署的应用，否则验证码生产与验证在节点之间信息不同步，导致失败。
 * <p>
 * ☆☆☆ SPI： 在resources目录新建META-INF.services文件夹(两层)，参考当前服务resources。
 *
 * <AUTHOR>
 * @Title: 使用redis缓存
 * @date 2020-05-12
 */
@RequiredArgsConstructor
public class CaptchaCacheServiceRedisImpl implements CaptchaCacheService {





    @Override
    public String type() {
        return "redis";
    }

    private static final String LUA_SCRIPT = "local key = KEYS[1] " +
        "local incrementValue = tonumber(ARGV[1]) " +
        "if redis.call('EXISTS', key) == 1 then " +
        "    return redis.call('INCRBY', key, incrementValue) " +
        "else " +
        "    return incrementValue " +
        "end";

    public void setStringRedisTemplate(StringRedisTemplate stringRedisTemplate) {
        this.stringRedisTemplate = stringRedisTemplate;

    }

//    @Autowired
    private  StringRedisTemplate stringRedisTemplate;

    @Override
    public void set(String key, String value, long expiresInSeconds) {
        RedisUtils.setCacheObject(key, value, Duration.ofSeconds(expiresInSeconds));
//        stringRedisTemplate.opsForValue().set(key, value, expiresInSeconds, TimeUnit.SECONDS);
    }

    @Override
    public boolean exists(String key) {
       return RedisUtils.hasKey(key);
//        return stringRedisTemplate.hasKey(key);
    }

    @Override
    public void delete(String key) {
        RedisUtils.deleteObject(key);
//        stringRedisTemplate.delete(key);
    }

    @Override
    public String get(String key) {
       return RedisUtils.getCacheObject(key);
//        return stringRedisTemplate.opsForValue().get(key);
    }

    @Override
    public Long increment(String key, long val) {
        // 执行 Lua 脚本
//        RedisScript<Long> script = new DefaultRedisScript<>(LUA_SCRIPT, Long.class);
//        // 执行 Lua 脚本
//        return stringRedisTemplate.execute(
//            script,
//            Collections.singletonList(key),
//            String.valueOf(val)
//        );

        RScript rScript = RedisUtils.getRScript();
        // 执行 Lua 脚本，返回 Long 类型结果
        return rScript.eval(
            RScript.Mode.READ_WRITE,    // 读写模式
            LUA_SCRIPT,                 // Lua 脚本内容
            RScript.ReturnType.INTEGER, // 返回类型为整数
            Collections.singletonList(key), // KEYS[1]
            String.valueOf(val)         // ARGV[1]
        );
    }

    @Override
    public void setExpire(String key, long l) {
        RedisUtils.expire(key, l);
//        stringRedisTemplate.expire(key, l, TimeUnit.SECONDS);
    }
}



