# OTO-life 项目开发规范（Rule 文档）

## AI助手交互规范

**重要提醒：每次任务执行过程中，AI助手必须使用 interactive-feedback 工具与开发者进行交互，寻求下一步指令和确认。这确保了开发过程的可控性和准确性。**

- 在开始任何重要操作前，必须通过 feedback 确认需求和实现方案
- 在完成阶段性工作后，必须通过 feedback 汇报进展并询问下一步
- 遇到技术选择或设计决策时，必须通过 feedback 与开发者讨论
- 代码生成完成后，必须通过 feedback 确认是否需要进一步优化或调整

## 项目概述

OTO-Home 是一个基于 RuoYi-Vue-Plus 框架开发的企业级管理系统，采用前后端分离架构。项目包含两个主要入口：
- **oto-admin**: 后台管理系统入口（端口8080），用于系统管理、用户管理、权限控制等后台功能
- **oto-front**: 会员业务系统入口（端口8081），面向终端用户提供业务服务

本文档旨在为开发团队（包括程序员和AI助手）提供统一的开发规范，确保代码质量和项目的可维护性。

## 一、项目目录结构规范

```
oto-home/
├── oto-admin/                  # 后台管理系统入口（端口8080）
│   ├── src/main/java/com/oto/
│   │   ├── OtoApplication.java      # 后台管理启动类
│   │   ├── web/                     # 后台管理控制器
│   │   └── config/                  # 后台管理配置
│   └── src/main/resources/
│       ├── application.yml          # 后台管理配置文件
│       └── logback.xml
│
├── oto-front/                  # 会员业务系统入口（端口8081）
│   ├── src/main/java/com/oto/
│   │   ├── OtoFrontApplication.java # 会员业务启动类
│   │   ├── web/                     # 会员业务控制器
│   │   └── config/                  # 会员业务配置
│   └── src/main/resources/
│       ├── application.yml          # 会员业务配置文件
│       └── logback.xml
│
├── oto-admin-modules/          # 后台管理功能模块
│   ├── oto-system/             # 系统管理（用户、角色、权限、菜单）
│   ├── oto-gen/                # 代码生成器
│   ├── oto-job/                # 定时任务管理
│   └── oto-file/               # 文件管理
│
├── oto-front-modules/          # 会员业务功能模块
│   ├── oto-member/             # 会员管理模块
│   │   ├── src/main/java/com/oto/member/
│   │   │   ├── controller/     # 控制器（API入口）
│   │   │   ├── service/        # 业务服务（接口+实现 impl/）
│   │   │   ├── mapper/         # MyBatis Mapper接口
│   │   │   └── domain/         # 领域对象（entity/bo/vo/model等）
│   │   └── src/main/resources/ # 资源文件（mapper XML等）
│   ├── oto-course/             # 课程管理
│   ├── oto-coach/              # 教练管理
│   ├── oto-equipment/          # 器材管理
│   ├── oto-reservation/        # 预约管理
│   ├── oto-payment/            # 支付管理
│   ├── oto-notification/       # 通知管理
│   └── oto-front-system-config/ # 前端系统配置模块（金刚位配置管理）
│
├── oto-common/                 # 公共模块（两个入口共享）
│   ├── oto-common-core/        # 核心工具类
│   ├── oto-common-dict/        # 字典服务
│   ├── oto-common-excel/       # Excel处理
│   ├── oto-common-mybatis/     # MyBatis配置
│   ├── oto-common-redis/       # Redis配置
│   ├── oto-common-satoken/     # 权限认证
│   ├── oto-common-security/    # 安全配置
│   ├── oto-common-sensitive/   # 敏感信息处理
│   ├── oto-common-sms/         # 短信服务
│   ├── oto-common-oss/         # 对象存储
│   └── ...                     # 其它通用模块
│
├── oto-extend/                 # 扩展模块（监控、任务调度等）
│   ├── oto-monitor-admin/      # 系统监控管理
│   └── oto-snailjob-server/    # 分布式任务调度
│
├── script/                     # 部署、运维、SQL等脚本
├── logs/                       # 日志输出目录
├── README.md                   # 项目说明文档
└── pom.xml                     # 主POM文件
```

### 双入口架构设计原则
- **入口分离**：oto-admin（后台管理）和oto-front（会员业务）独立部署，端口分离。
- **模块隔离**：admin-modules和front-modules功能模块完全隔离，避免业务耦合。
- **分层清晰**：controller、service、mapper、domain（entity/bo/vo/model）分层明确，禁止混用。
- **单一职责**：每个包/类只负责一类功能，便于维护和扩展。
- **资源分离**：Java代码与资源文件（如XML、yml）分离，resources下仅放配置和Mapper XML。
- **公共复用**：oto-common模块为两个入口提供共享的基础能力。

### 双入口架构详细说明

#### oto-admin（后台管理入口）
- **端口**: 8080
- **用途**: 系统管理员使用的后台管理界面
- **主要功能**: 
  - 用户权限管理
  - 系统配置管理
  - 数据统计分析
  - 代码生成工具
  - 系统监控
- **依赖模块**: oto-admin-modules、oto-common、oto-extend

#### oto-front（会员业务入口）
- **端口**: 8081
- **用途**: 面向终端用户的业务服务接口
- **主要功能**:
  - 会员注册登录
  - 业务功能操作
  - 移动端API支持
  - 第三方集成
- **依赖模块**: oto-front-modules、oto-common

---

## 二、代码封装与调用规范

### 1. Controller 层
- 只负责参数接收、权限校验、调用 service，禁止写业务逻辑。
- 返回统一响应对象（如 Result/Response/VO），不暴露 entity。

### 2. Service 层
- 业务逻辑集中，接口与实现分离（如 IOtoMemberService + impl/）。
- 复杂业务拆分为私有方法，避免超长方法。
- 事务控制在 service 层，controller 禁止加事务注解。

### 3. Mapper 层
- 只负责数据库操作，禁止写业务逻辑。
- SQL 统一放在 resources/mapper 下 XML 文件，接口与 XML 命名一致。

### 4. Domain 层
- entity：数据库实体，禁止带业务方法。
- bo（Business Object）：接收前端参数，禁止用于返回。
- vo（View Object）：返回前端的数据结构，禁止用于接收参数。
- model：领域模型，复杂业务可引入。

### 5. 工具与通用类
- 工具类统一放在 oto-common 下，禁止在业务模块重复造轮子。
- 加密、日志、缓存等通用能力必须通过工具类或中间件调用，禁止硬编码。

---

## 三、可读性与可维护性规范

### 1. 命名规范
- 包名、类名、方法名、变量名必须见名知意，禁止拼音、缩写、无意义命名。
- 常量大写，驼峰命名，接口以 I 开头，impl 为实现类后缀。

### 2. 注释规范
- 类、方法、复杂逻辑必须有注释，描述用途、参数、返回值、异常。
- 重要业务流程、关键算法需有详细注释，便于审计和交接。

### 3. 代码风格
- 严格遵守 Java 编码规范，IDE 配置统一（如 .editorconfig）。
- 每行不超过120字符，方法不超过80行，类不超过1000行。
- 禁止魔法值，所有常量集中管理。

### 4. 依赖管理
- 依赖版本统一由父pom管理，禁止在子模块随意指定版本。
- 业务模块依赖通用模块，禁止反向依赖。

---

## 四、扩展性与安全合规

### 1. 扩展性
- 新业务模块必须独立建包，禁止在其它模块夹带代码。
- 公共能力优先抽取到 common 层，避免重复开发。
- 所有接口预留扩展点（如预留扩展字段、钩子方法）。

### 2. 安全合规
- 敏感数据（手机号、身份证等）必须加密存储，严禁明文。
- 日志、异常、审计必须留痕，便于溯源。
- 所有外部接口需鉴权，禁止未授权访问。

---

## 五、AI/程序员友好性

- 目录、包、类、方法命名必须一目了然，便于AI自动分析和代码生成。
- 业务流程、数据流、调用链路需有文档或注释说明，便于AI和新同事快速上手。
- 重要变更、设计决策需在README或专门文档中记录。

## 六、工具类使用规范

### 6.1 优先使用项目封装的工具类
- **严禁重复创建已存在的工具类**
- 优先使用 `com.oto.common.core.utils` 包下的工具类
- 常用工具类包括：
  - `StringUtils`: 字符串处理
  - `DateUtils`: 日期时间处理
  - `ObjectUtils`: 对象操作
  - `ValidatorUtils`: 数据验证
  - `ServletUtils`: Web请求处理
  - `MessageUtils`: 国际化消息
  - `TreeBuildUtils`: 树形结构构建
  - `MapstructUtils`: 对象映射

### 6.2 工具类扩展原则
- 如需新增工具方法，优先扩展现有工具类
- 新建工具类需遵循项目命名规范
- 工具类必须提供完整的JavaDoc注释

## 七、数据安全与隐私合规规范

### 7.1 敏感数据处理
- **手机号**: 使用AES加密存储，同时存储SHA256哈希用于查询
- **身份证号**: 使用AES加密存储，同时存储SHA256哈希用于验证
- **真实姓名**: 使用AES加密存储
- **邮箱**: 根据敏感级别决定是否加密

### 7.2 数据脱敏规范
- VO对象中敏感数据必须脱敏显示
- 手机号脱敏格式：`138****1234`
- 身份证脱敏格式：`330***********1234`
- 真实姓名脱敏格式：`张**`

### 7.3 隐私合规要求
- 必须记录用户数据使用同意状态
- 支持数据保留期限设置（默认3年）
- 提供数据匿名化处理机制
- 记录隐私协议版本和同意时间

## 八、Bean Validation验证规范

### 8.1 验证分组使用
- `AddGroup`: 新增操作验证组
- `EditGroup`: 编辑操作验证组
- `QueryGroup`: 查询操作验证组

### 8.2 常用验证注解
- `@NotNull`: 非空验证，指定验证组
- `@NotBlank`: 非空字符串验证
- `@Size`: 字符串长度验证
- `@Pattern`: 正则表达式验证
- `@Email`: 邮箱格式验证
- `@Min/@Max`: 数值范围验证

### 8.3 验证消息规范
- 提供清晰的中文错误提示
- 错误消息格式：`字段名+验证要求`
- 示例：`用户名不能为空`、`手机号格式不正确`

## 九、数据对象使用规范

### 9.1 Entity（实体类）
- 对应数据库表结构
- 使用MyBatis-Plus注解
- 包含完整的数据库字段映射
- 敏感字段使用加密注解

### 9.2 BO（Business Object）
- 用于接收前端请求参数
- 包含完整的Bean Validation验证
- 支持验证分组
- 字段注释清晰明确

### 9.3 VO（View Object）
- 用于返回给前端的数据
- 敏感数据必须脱敏处理
- 使用`@AutoMapper`注解自动映射
- 实现`Serializable`接口

### 9.4 DTO（Data Transfer Object）
- 用于系统间数据传输
- 根据业务场景定制字段
- 保持数据传输的轻量化

## 十、异常处理规范

### 10.1 异常分类
- 使用项目定义的异常类型
- `ServiceException`: 业务异常
- `SseException`: SSE相关异常
- 按模块划分异常包结构

### 10.2 异常处理原则
- 业务异常使用`ServiceException`
- 提供清晰的错误码和错误信息
- 避免向用户暴露系统内部错误

## 十一、公共组件使用规范

### 11.1 安全组件
- 前端安全：`oto-common-front-security`
- 后台安全：`oto-common-admin-security`
- Token管理：`oto-common-satoken`

### 11.2 数据处理组件
- 数据库操作：`oto-common-mybatis`
- 缓存操作：`oto-common-redis`
- Excel处理：`oto-common-excel`
- 加密处理：`oto-common-encrypt`

### 11.3 通信组件
- 邮件发送：`oto-common-mail`
- 短信发送：`oto-common-sms`
- WebSocket：`oto-common-websocket`
- SSE推送：`oto-common-sse`

---

## 十二、目录结构举例（以 oto-member 为例）

```
oto-member/
├── src/main/java/com/oto/member/
│   ├── controller/           # 控制器
│   │   └── OtoMemberTestController.java
│   ├── service/              # 业务接口与实现
│   │   ├── IOtoMemberService.java
│   │   └── impl/
│   ├── mapper/               # MyBatis接口
│   │   └── OtoUserMapper.java
│   └── domain/               # 领域对象
│       ├── entity/           # 实体
│       ├── bo/               # 业务对象
│       ├── vo/               # 视图对象
│       └── model/            # 领域模型
└── src/main/resources/
    └── mapper/               # MyBatis XML
```

---

## 十三、其它约定

- 所有新建模块、包、类、接口、方法必须严格遵守本规范，违者需重构。
- 代码合并前必须自查规范，代码评审严格按本规范执行。
- 规范持续迭代，重大变更需团队评审通过。

---

## 十四、oto-front-system-config 模块规范

### 14.1 模块概述
`oto-front-system-config` 模块是前端系统配置管理模块，主要负责金刚位（Grid）配置管理功能。该模块提供了完整的金刚位分组、金刚位配置项的管理能力。

### 14.2 模块结构
```
oto-front-system-config/
├── src/main/java/com/oto/front/config/
│   ├── controller/                    # 控制器层
│   │   ├── OtoGridGroupController.java      # 金刚位分组管理
│   │   ├── OtoGridItemController.java       # 金刚位配置管理

│   ├── service/                       # 服务层
│   │   ├── IOtoGridGroupService.java        # 金刚位分组服务接口
│   │   ├── IOtoGridItemService.java         # 金刚位配置服务接口

│   │   └── impl/                      # 服务实现层
│   │       ├── OtoGridGroupServiceImpl.java
│   │       ├── OtoGridItemServiceImpl.java

│   ├── mapper/                        # 数据访问层
│   │   ├── OtoGridGroupMapper.java          # 金刚位分组Mapper
│   │   ├── OtoGridItemMapper.java           # 金刚位配置Mapper

│   └── domain/                        # 领域对象
│       ├── entity/                    # 实体对象
│       │   ├── OtoGridGroup.java            # 金刚位分组实体
│       │   ├── OtoGridItem.java             # 金刚位配置实体

│       ├── bo/                        # 业务对象
│       │   ├── OtoGridGroupBo.java          # 金刚位分组业务对象
│       │   ├── OtoGridItemBo.java           # 金刚位配置业务对象

│       └── vo/                        # 视图对象
│           ├── OtoGridGroupVo.java          # 金刚位分组视图对象
│           ├── OtoGridItemVo.java           # 金刚位配置视图对象

└── src/main/resources/
    └── mapper/                        # MyBatis XML映射文件
        ├── OtoGridGroupMapper.xml
        ├── OtoGridItemMapper.xml
        
```

### 14.3 核心功能

#### 14.3.1 金刚位分组管理（OtoGridGroup）
- **功能描述**: 管理金刚位的分组信息，支持不同平台（iOS、Android、Web、小程序）的分组配置
- **主要字段**: 
  - `groupName`: 分组名称
  - `groupType`: 分组类型（default、custom、recommend、activity）
  - `platform`: 平台类型（ios、android、web、miniprogram）
  - `sortOrder`: 排序值
  - `maxItems`: 最大显示数量
  - `layoutConfig`: 布局配置（JSON格式）
  - `isActive`: 是否启用

#### 14.3.2 金刚位配置管理（OtoGridItem）
- **功能描述**: 管理具体的金刚位配置项，包括图标、标题、动作等信息
- **主要字段**:
  - `groupId`: 所属分组ID
  - `title`: 标题
  - `subtitle`: 副标题
  - `iconUrl`: 图标URL
  - `backgroundColor`: 背景颜色
  - `textColor`: 文字颜色
  - `actionType`: 动作类型（url、route、api、popup）
  - `actionValue`: 动作值
  - `actionParams`: 动作参数（JSON格式）
  - `sortOrder`: 排序值
  - `isHot`: 是否热门
  - `isNew`: 是否新品



  - `gridItemId`: 金刚位ID
  - `actionType`: 操作类型（click、hide、sort、favorite）
  - `customSort`: 自定义排序值
  - `isHidden`: 是否隐藏
  - `clickCount`: 点击次数
  - `lastClickTime`: 最后点击时间

### 14.4 数据库设计
模块对应的数据库表结构请参考 `/script/sql/oto_grid_system.sql` 文件，包含：
- `oto_grid_group`: 金刚位分组表
- `oto_grid_item`: 金刚位配置表


### 14.5 开发注意事项

#### 14.5.1 字段映射注意事项
- Entity类中的字段名必须与数据库表字段名保持一致
- Bo和Vo类中的字段名应使用驼峰命名法
- 特别注意以下字段映射：
  - `max_display_count` (数据库) ↔ `maxItems` (Java)
  - `is_active` (数据库) ↔ `isActive` (Java)
  - `group_name` (数据库) ↔ `groupName` (Java)

#### 14.5.2 Service层实现规范
- 所有Mapper方法调用必须使用正确的方法名
- 对于数据库中不存在的方法，应使用LambdaQueryWrapper进行查询
- 示例：
```java
// 正确的查询方式
public List<OtoGridGroup> queryByType(String groupType) {
    LambdaQueryWrapper<OtoGridGroup> wrapper = Wrappers.lambdaQuery();
    wrapper.eq(OtoGridGroup::getGroupType, groupType);
    return baseMapper.selectList(wrapper);
}
```

#### 14.5.3 编译验证
在修改模块代码后，必须执行以下命令验证编译：
```bash
mvn clean compile -pl oto-admin-modules/oto-front-system-config -am
```

### 14.6 API接口规范
所有Controller必须遵循统一的接口规范：
- 使用`@RestController`和`@RequestMapping`注解
- 继承`BaseController`类
- 使用`@SaCheckPermission`进行权限控制
- 使用`@Log`记录操作日志
- 返回统一的响应格式（`R<T>`或`TableDataInfo<T>`）

---

**如需自动化生成代码、文档、或AI辅助开发，强烈建议严格遵循本 rule 文档，确保项目长期可维护、可扩展、可交接。**

**如需补充具体代码示例或特殊场景规范，请随时补充！** 


# 企业级开发规范文档

## 1. 项目架构与模块划分

### 1.1 模块结构规范
```
RuoYi-Vue-Plus/
├── oto-admin-modules/      # 系统核心模块（不可修改）
│   ├── ruoyi-system/       # 系统管理模块
│   ├── ruoyi-demo/         # 示例模块
│   ├── ruoyi-generator/    # 代码生成模块
│   ├── ruoyi-job/          # 定时任务模块
│   └── ruoyi-workflow/     # 工作流模块
├── oto-front-modules/      # 业务模块（自定义开发）
│   ├── oto-member/         # 会员管理模块
│   ├── oto-course/         # 课程管理模块
│   ├── oto-order/          # 订单管理模块
│   ├── oto-front-system-config/ # 前端系统配置模块（金刚位配置管理）
│   └── oto-{业务名}/       # 其他业务模块
└── oto-common/             # 通用组件模块
```

### 1.2 包结构规范
每个业务模块必须按以下结构组织：
```
src/main/java/org/dromara/{module}/
├── controller/             # 控制器层
├── service/               # 服务层
│   └── impl/              # 服务实现层
├── mapper/                # 数据访问层
├── domain/                # 领域模型
│   ├── bo/                # 业务对象(Business Object)
│   ├── vo/                # 视图对象(View Object)
│   └── entity/            # 实体对象(Entity)
├── config/                # 配置类
├── listener/              # 监听器
└── enums/                 # 枚举类
```

## 2. 命名规范

### 2.1 类命名规范

#### 2.1.1 Controller层
- **命名格式**: `{业务名}Controller`
- **示例**: `OtoUserController`, `OtoCourseController`
- **继承**: 必须继承 `BaseController`
- **注解**: 必须添加 `@RestController`, `@RequestMapping`, `@Validated`, `@RequiredArgsConstructor`

```java
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/member/user")
public class OtoUserController extends BaseController {
    // 控制器实现
}
```

#### 2.1.2 Service层
- **接口命名**: `I{业务名}Service`
- **实现类命名**: `{业务名}ServiceImpl`
- **示例**: `IOtoUserService`, `OtoUserServiceImpl`

```java
// 接口
public interface IOtoUserService {
    // 服务方法定义
}

// 实现类
@RequiredArgsConstructor
@Service
public class OtoUserServiceImpl implements IOtoUserService {
    // 服务方法实现
}
```

#### 2.1.3 Mapper层
- **命名格式**: `{业务名}Mapper`
- **示例**: `OtoUserMapper`
- **注解**: 必须添加 `@Mapper`

```java
@Mapper
public interface OtoUserMapper extends BaseMapperPlus<OtoUser, OtoUserVo> {
    // 自定义查询方法
}
```

#### 2.1.4 Domain层
- **Entity**: `{业务名}` (如: `OtoUser`)
- **BO**: `{业务名}Bo` (如: `OtoUserBo`)
- **VO**: `{业务名}Vo` (如: `OtoUserVo`)

### 2.2 方法命名规范

#### 2.2.1 Controller层方法
- **查询列表**: `list(Bo, PageQuery)`
- **查询详情**: `getInfo(@PathVariable Long id)`
- **新增**: `add(@RequestBody Bo)`
- **修改**: `edit(@RequestBody Bo)`
- **删除**: `remove(@PathVariable Long[] ids)`
- **导出**: `export(Bo, HttpServletResponse)`

#### 2.2.2 Service层方法
- **分页查询**: `queryPageList(Bo, PageQuery)`
- **列表查询**: `queryList(Bo)`
- **详情查询**: `queryById(Long id)`
- **新增**: `insertByBo(Bo)`
- **修改**: `updateByBo(Bo)`
- **删除**: `deleteWithValidByIds(List<Long>, boolean)`

#### 2.2.3 业务方法命名
- **检查方法**: `check{业务}` (如: `checkUserExists`)
- **验证方法**: `validate{业务}` (如: `validatePassword`)
- **构建方法**: `build{业务}` (如: `buildUserInfo`)
- **转换方法**: `convert{业务}` (如: `convertToVo`)

### 2.3 常量命名规范
- **类名**: 以 `Constants` 结尾
- **常量名**: 全大写，单词间用下划线分隔
- **示例**: `USER_TYPE_STUDENT`, `ORDER_STATUS_PAID`

## 3. 注解使用规范

### 3.1 权限控制注解
```java
// 权限检查 - 必须使用
@SaCheckPermission("member:user:list")
@GetMapping("/list")
public TableDataInfo<OtoUserVo> list(OtoUserBo bo, PageQuery pageQuery) {
    return userService.queryPageList(bo, pageQuery);
}
```

### 3.2 日志记录注解
```java
// 操作日志 - 增删改操作必须使用
@Log(title = "用户管理", businessType = BusinessType.INSERT)
@PostMapping()
public R<Void> add(@RequestBody OtoUserBo bo) {
    return toAjax(userService.insertByBo(bo));
}
```

### 3.3 防重复提交注解
```java
// 防重复提交 - 重要操作必须使用
@RepeatSubmit()
@PostMapping()
public R<Void> add(@RequestBody OtoUserBo bo) {
    return toAjax(userService.insertByBo(bo));
}
```

### 3.4 数据脱敏注解
```java
// 敏感数据脱敏
@Sensitive(strategy = SensitiveStrategy.PHONE)
private String phone;

@Sensitive(strategy = SensitiveStrategy.EMAIL)
private String email;

@Sensitive(strategy = SensitiveStrategy.CHINESE_NAME)
private String realName;
```

### 3.5 数据验证注解
```java
// Bo类验证规范
@NotBlank(message = "用户名不能为空", groups = {AddGroup.class, EditGroup.class})
@Size(min = 2, max = 30, message = "用户名长度必须在2-30个字符之间")
@Pattern(regexp = "^[a-zA-Z0-9_\\u4e00-\\u9fa5]+$", message = "用户名只能包含字母、数字、下划线和中文")
private String username;

@NotBlank(message = "手机号不能为空", groups = {AddGroup.class, EditGroup.class})
@Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
private String phone;

@Email(message = "邮箱格式不正确")
private String email;
```

## 4. 缓存使用规范

### 4.1 缓存命名规范
```java
// 定义缓存常量类
public class CacheNames {
    // 业务缓存命名格式: {模块}_{业务}_{缓存类型}
    public static final String OTO_USER = "oto_user";
    public static final String OTO_USER_INFO = "oto_user_info";
    public static final String OTO_COURSE = "oto_course";
}
```

### 4.2 缓存注解使用
```java
// 查询缓存
@Cacheable(cacheNames = CacheNames.OTO_USER, key = "#userId")
public OtoUserVo queryById(Long userId) {
    return baseMapper.selectVoById(userId);
}

// 更新缓存
@CachePut(cacheNames = CacheNames.OTO_USER, key = "#bo.userId")
public boolean updateByBo(OtoUserBo bo) {
    return baseMapper.updateById(BeanUtil.toBean(bo, OtoUser.class)) > 0;
}

// 删除缓存
@CacheEvict(cacheNames = CacheNames.OTO_USER, key = "#userId")
public boolean deleteById(Long userId) {
    return baseMapper.deleteById(userId) > 0;
}

// 条件缓存
@Cacheable(cacheNames = CacheNames.OTO_USER, key = "#userId", condition = "#userId != null")
public OtoUserVo queryByIdWithCondition(Long userId) {
    return baseMapper.selectVoById(userId);
}
```

### 4.3 缓存策略
- **热点数据**: 使用 `@Cacheable` 进行缓存
- **用户信息**: 缓存时间设置为30分钟
- **配置信息**: 缓存时间设置为1小时
- **字典数据**: 缓存时间设置为24小时
- **频繁变更数据**: 不使用缓存或使用短时间缓存(5分钟)

## 5. 数据库设计规范

### 5.1 表命名规范
- **业务表**: `oto_{业务名}` (如: `oto_user`, `oto_course`)
- **关联表**: `oto_{主表}_{从表}` (如: `oto_user_course`)
- **日志表**: `oto_{业务名}_log` (如: `oto_user_log`)

### 5.2 字段命名规范
- **主键**: `{表名}_id` (如: `user_id`, `course_id`)
- **外键**: `{关联表名}_id` (如: `user_id`, `course_id`)
- **状态字段**: `status`, `is_{状态名}` (如: `is_vip`, `is_deleted`)
- **时间字段**: `{动作}_time` (如: `create_time`, `update_time`)

### 5.3 必须字段
每个业务表必须包含以下字段：
```sql
CREATE TABLE oto_example (
    example_id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    -- 业务字段...
    tenant_id VARCHAR(20) DEFAULT '000000' COMMENT '租户编号',
    create_dept BIGINT COMMENT '创建部门',
    create_by BIGINT COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by BIGINT COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (example_id)
) COMMENT = '示例表';
```

## 6. 异常处理规范

### 6.1 异常分类
```java
// 业务异常 - 继承ServiceException
public class UserException extends ServiceException {
    public UserException(String message) {
        super(message);
    }
}

// 使用示例
if (user == null) {
    throw new UserException("用户不存在");
}
```

### 6.2 统一错误码
```java
// 错误码常量类
public class ErrorCode {
    // 用户相关错误码 10001-10999
    public static final int USER_NOT_FOUND = 10001;
    public static final int USER_ALREADY_EXISTS = 10002;
    
    // 课程相关错误码 20001-20999
    public static final int COURSE_NOT_FOUND = 20001;
    public static final int COURSE_FULL = 20002;
}
```

## 7. 代码生成交互规范

### 7.1 生成前确认流程
在生成代码前，必须与开发者确认以下信息：

1. **表结构确认**
   - 表名是否符合命名规范
   - 字段类型是否正确
   - 是否包含必须字段

2. **功能范围确认**
   - 需要生成哪些基础功能(增删改查)
   - 是否需要导入导出功能
   - 是否需要特殊业务方法

3. **权限设计确认**
   - 权限码设计
   - 菜单结构设计
   - 角色分配策略

### 7.2 生成后验证流程
代码生成完成后，必须进行以下验证：

1. **编译验证**
```bash
# 编译验证命令
mvn clean compile -pl oto-front-modules/oto-{模块名}
```

2. **单元测试验证**
```bash
# 运行单元测试
mvn test -pl oto-front-modules/oto-{模块名}
```

3. **接口测试验证**
   - 使用Postman或Swagger测试所有接口
   - 验证参数验证是否生效
   - 验证返回数据格式是否正确

## 8. 接口文档规范

### 8.1 Swagger注解规范
```java
@Tag(name = "用户管理", description = "用户相关接口")
@RestController
public class OtoUserController {
    
    @Operation(summary = "查询用户列表", description = "分页查询用户信息")
    @Parameters({
        @Parameter(name = "username", description = "用户名"),
        @Parameter(name = "phone", description = "手机号"),
        @Parameter(name = "status", description = "状态")
    })
    @GetMapping("/list")
    public TableDataInfo<OtoUserVo> list(OtoUserBo bo, PageQuery pageQuery) {
        return userService.queryPageList(bo, pageQuery);
    }
}
```

### 8.2 接口文档内容要求
每个接口必须包含：
- **接口描述**: 清晰说明接口功能
- **请求参数**: 详细说明每个参数的含义、类型、是否必填
- **返回参数**: 说明返回数据的结构和含义
- **错误码**: 列出可能的错误情况和错误码
- **示例**: 提供请求和响应示例

## 9. 性能优化规范

### 9.1 数据库查询优化
```java
// 使用分页查询，避免查询大量数据
public TableDataInfo<OtoUserVo> queryPageList(OtoUserBo bo, PageQuery pageQuery) {
    LambdaQueryWrapper<OtoUser> lqw = buildQueryWrapper(bo);
    Page<OtoUserVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
    return TableDataInfo.build(result);
}

// 构建查询条件，使用索引字段
private LambdaQueryWrapper<OtoUser> buildQueryWrapper(OtoUserBo bo) {
    LambdaQueryWrapper<OtoUser> lqw = Wrappers.lambdaQuery();
    lqw.eq(StringUtils.isNotBlank(bo.getUsername()), OtoUser::getUsername, bo.getUsername());
    lqw.eq(StringUtils.isNotBlank(bo.getPhone()), OtoUser::getPhone, bo.getPhone());
    lqw.eq(bo.getStatus() != null, OtoUser::getStatus, bo.getStatus());
    return lqw;
}
```

### 9.2 缓存优化
```java
// 批量查询优化
@Cacheable(cacheNames = CacheNames.OTO_USER, key = "#userIds")
public List<OtoUserVo> queryByIds(List<Long> userIds) {
    return baseMapper.selectVoByIds(userIds);
}

// 缓存预热
@EventListener(ApplicationReadyEvent.class)
public void warmUpCache() {
    // 预热热点数据
    loadHotUserData();
}
```

## 10. 测试规范

### 10.1 单元测试规范
```java
@SpringBootTest
@Transactional
@Rollback
class OtoUserServiceTest {
    
    @Autowired
    private IOtoUserService userService;
    
    @Test
    @DisplayName("测试用户注册功能")
    void testUserRegister() {
        // Given
        OtoUserBo userBo = createTestUser();
        
        // When
        boolean result = userService.insertByBo(userBo);
        
        // Then
        assertTrue(result);
        assertNotNull(userService.queryById(userBo.getUserId()));
    }
}
```

### 10.2 集成测试规范
```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase
class OtoUserControllerTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    @DisplayName("测试用户列表查询接口")
    void testUserList() {
        // 测试接口调用
        ResponseEntity<TableDataInfo> response = restTemplate.getForEntity(
            "/member/user/list", TableDataInfo.class);
        
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
    }
}
```

## 11. 安全规范

### 11.1 数据验证
```java
// 输入验证
@PostMapping()
public R<Void> add(@Validated(AddGroup.class) @RequestBody OtoUserBo bo) {
    // 业务验证
    validateUserData(bo);
    return toAjax(userService.insertByBo(bo));
}

private void validateUserData(OtoUserBo bo) {
    if (userService.existsByUsername(bo.getUsername())) {
        throw new UserException("用户名已存在");
    }
    if (userService.existsByPhone(bo.getPhone())) {
        throw new UserException("手机号已被注册");
    }
}
```

### 11.2 权限控制
```java
// 数据权限控制
@SaCheckPermission("member:user:list")
@GetMapping("/list")
public TableDataInfo<OtoUserVo> list(OtoUserBo bo, PageQuery pageQuery) {
    // 添加数据权限过滤
    addDataScopeFilter(bo);
    return userService.queryPageList(bo, pageQuery);
}
```

## 12. 代码提交规范

### 12.1 Git提交规范
```bash
# 提交格式: type(scope): description
feat(user): 新增用户注册功能
fix(course): 修复课程查询bug
docs(api): 更新接口文档
style(format): 代码格式调整
refactor(service): 重构用户服务层
test(unit): 添加用户单元测试
chore(deps): 更新依赖版本
```

### 12.2 代码审查清单
提交代码前必须检查：
- [ ] 代码符合命名规范
- [ ] 添加了必要的注释
- [ ] 包含单元测试
- [ ] 通过所有测试用例
- [ ] 没有硬编码配置
- [ ] 正确处理异常
- [ ] 使用了合适的缓存策略
- [ ] 接口文档已更新

## 13. 部署与运维规范

### 13.1 环境配置
```yaml
# application-{env}.yml 环境配置规范
server:
  port: ${SERVER_PORT:8080}

spring:
  datasource:
    url: ${DB_URL:***************************************}
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:password}

# 缓存配置
redisson:
  address: ${REDIS_URL:redis://localhost:6379}
  password: ${REDIS_PASSWORD:}
```

### 13.2 监控规范
```java
// 添加监控指标
@Component
public class UserMetrics {
    
    private final MeterRegistry meterRegistry;
    private final Counter userRegisterCounter;
    
    public UserMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.userRegisterCounter = Counter.builder("user.register.count")
            .description("用户注册数量")
            .register(meterRegistry);
    }
    
    public void incrementRegisterCount() {
        userRegisterCounter.increment();
    }
}
```

---

## 总结

本规范基于RuoYi-Vue-Plus框架制定，旨在确保团队开发的一致性和代码质量。所有开发人员必须严格遵守本规范，任何变更都需要团队讨论决定。

**重要提醒**：
1. 在生成任何代码前，必须与开发者确认需求和设计
2. 代码生成后必须进行编译和测试验证
3. 接口开发完成后必须更新文档
4. 提交代码前必须通过代码审查清单检查

规范将根据项目发展和技术演进持续更新，请关注最新版本。