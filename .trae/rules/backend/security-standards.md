# OTO-Home 安全开发规范

> **适用范围**：oto-admin 和 oto-front 模块的安全开发
> **安全框架**：Spring Security + JWT + Redis

---

## 🔐 认证授权体系

### JWT Token管理

#### Token结构设计
```json
{
  "header": {
    "typ": "JWT",
    "alg": "HS256"
  },
  "payload": {
    "userId": "1001",
    "userName": "admin",
    "roles": ["ADMIN", "USER"],
    "permissions": ["user:read", "user:write"],
    "exp": 1640995200,
    "iat": 1640908800
  }
}
```

#### Token生成与验证
```java
@Component
public class JwtTokenUtil {
    
    private static final String SECRET = "${jwt.secret}";
    private static final long EXPIRATION = 7200; // 2小时
    
    public String generateToken(UserDetails userDetails) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", userDetails.getUserId());
        claims.put("roles", userDetails.getRoles());
        return createToken(claims, userDetails.getUsername());
    }
    
    public Boolean validateToken(String token, UserDetails userDetails) {
        final String username = getUsernameFromToken(token);
        return (username.equals(userDetails.getUsername()) && !isTokenExpired(token));
    }
}
```

### 权限控制模型

#### RBAC权限设计
- **用户（User）**：系统使用者
- **角色（Role）**：权限集合，如管理员、普通用户
- **权限（Permission）**：具体操作权限，如 `user:read`、`order:write`
- **资源（Resource）**：受保护的系统资源

#### 权限注解使用
```java
@RestController
@RequestMapping("/api/users")
public class UserController {
    
    @GetMapping
    @PreAuthorize("hasPermission('user', 'read')")
    public R<List<UserVO>> listUsers() {
        // 查询用户列表
    }
    
    @PostMapping
    @PreAuthorize("hasRole('ADMIN') or hasPermission('user', 'write')")
    public R<Void> createUser(@RequestBody UserCreateDTO userDTO) {
        // 创建用户
    }
    
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public R<Void> deleteUser(@PathVariable Long id) {
        // 删除用户
    }
}
```

---

## 🛡️ 数据安全防护

### 输入验证与过滤

#### 参数验证
```java
public class UserCreateDTO {
    
    @NotBlank(message = "用户名不能为空")
    @Pattern(regexp = "^[a-zA-Z0-9_]{4,20}$", message = "用户名格式不正确")
    private String userName;
    
    @NotBlank(message = "密码不能为空")
    @Pattern(regexp = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{8,20}$", 
             message = "密码必须包含大小写字母和数字，长度8-20位")
    private String password;
    
    @Email(message = "邮箱格式不正确")
    private String email;
    
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;
}
```

#### XSS防护
```java
@Component
public class XssFilter implements Filter {
    
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, 
                        FilterChain chain) throws IOException, ServletException {
        
        XssHttpServletRequestWrapper xssRequest = 
            new XssHttpServletRequestWrapper((HttpServletRequest) request);
        chain.doFilter(xssRequest, response);
    }
}

public class XssHttpServletRequestWrapper extends HttpServletRequestWrapper {
    
    @Override
    public String getParameter(String name) {
        String value = super.getParameter(name);
        return cleanXSS(value);
    }
    
    private String cleanXSS(String value) {
        if (value == null) return null;
        
        // 移除脚本标签
        value = value.replaceAll("<script[^>]*>.*?</script>", "");
        value = value.replaceAll("javascript:", "");
        value = value.replaceAll("vbscript:", "");
        value = value.replaceAll("onload", "");
        
        return value;
    }
}
```

### SQL注入防护

#### 参数化查询
```java
// 正确：使用参数化查询
@Select("SELECT * FROM sys_user WHERE user_name = #{userName} AND status = #{status}")
List<SysUser> selectByUserName(@Param("userName") String userName, 
                              @Param("status") Integer status);

// 错误：字符串拼接（容易SQL注入）
// String sql = "SELECT * FROM sys_user WHERE user_name = '" + userName + "'";
```

#### MyBatis-Plus安全使用
```java
// 安全的动态查询
QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
queryWrapper.eq("user_name", userName)
           .like(StringUtils.isNotBlank(keyword), "nick_name", keyword)
           .in(CollectionUtils.isNotEmpty(statusList), "status", statusList);
```

---

## 🔒 密码安全管理

### 密码加密存储
```java
@Service
public class PasswordService {
    
    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
    
    /**
     * 密码加密
     */
    public String encodePassword(String rawPassword) {
        return passwordEncoder.encode(rawPassword);
    }
    
    /**
     * 密码验证
     */
    public boolean matches(String rawPassword, String encodedPassword) {
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }
}
```

### 密码策略
- **复杂度要求**：至少8位，包含大小写字母、数字
- **有效期管理**：密码90天过期，提前7天提醒
- **历史密码**：不能与最近3次密码相同
- **错误锁定**：连续5次错误锁定账户30分钟

```java
@Service
public class LoginAttemptService {
    
    private static final int MAX_ATTEMPTS = 5;
    private static final int LOCK_TIME_MINUTES = 30;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    public void recordFailedAttempt(String username) {
        String key = "login_attempts:" + username;
        Integer attempts = (Integer) redisTemplate.opsForValue().get(key);
        
        if (attempts == null) {
            attempts = 0;
        }
        
        attempts++;
        redisTemplate.opsForValue().set(key, attempts, LOCK_TIME_MINUTES, TimeUnit.MINUTES);
        
        if (attempts >= MAX_ATTEMPTS) {
            lockAccount(username);
        }
    }
    
    public boolean isAccountLocked(String username) {
        String key = "account_locked:" + username;
        return redisTemplate.hasKey(key);
    }
}
```

---

## 🌐 网络安全配置

### HTTPS配置
```yaml
server:
  port: 8443
  ssl:
    enabled: true
    key-store: classpath:keystore.p12
    key-store-password: ${ssl.keystore.password}
    key-store-type: PKCS12
    key-alias: tomcat
```

### CORS配置
```java
@Configuration
public class CorsConfig {
    
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        
        // 允许的域名
        configuration.setAllowedOriginPatterns(Arrays.asList(
            "https://*.oto.com",
            "http://localhost:*"
        ));
        
        // 允许的HTTP方法
        configuration.setAllowedMethods(Arrays.asList(
            "GET", "POST", "PUT", "DELETE", "OPTIONS"
        ));
        
        // 允许的请求头
        configuration.setAllowedHeaders(Arrays.asList("*"));
        
        // 允许携带凭证
        configuration.setAllowCredentials(true);
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/api/**", configuration);
        
        return source;
    }
}
```

---

## 📝 安全日志与审计

### 安全事件记录
```java
@Component
public class SecurityAuditLogger {
    
    private static final Logger securityLogger = 
        LoggerFactory.getLogger("SECURITY_AUDIT");
    
    public void logLoginSuccess(String username, String ip) {
        securityLogger.info("LOGIN_SUCCESS - User: {}, IP: {}, Time: {}", 
                          username, ip, LocalDateTime.now());
    }
    
    public void logLoginFailure(String username, String ip, String reason) {
        securityLogger.warn("LOGIN_FAILURE - User: {}, IP: {}, Reason: {}, Time: {}", 
                          username, ip, reason, LocalDateTime.now());
    }
    
    public void logPermissionDenied(String username, String resource, String action) {
        securityLogger.warn("PERMISSION_DENIED - User: {}, Resource: {}, Action: {}, Time: {}", 
                          username, resource, action, LocalDateTime.now());
    }
}
```

### 敏感操作审计
```java
@Aspect
@Component
public class AuditAspect {
    
    @Autowired
    private AuditService auditService;
    
    @Around("@annotation(auditLog)")
    public Object around(ProceedingJoinPoint point, AuditLog auditLog) throws Throwable {
        
        String username = SecurityUtils.getCurrentUsername();
        String operation = auditLog.operation();
        String module = auditLog.module();
        
        try {
            Object result = point.proceed();
            
            // 记录成功操作
            auditService.recordAudit(username, module, operation, "SUCCESS", null);
            
            return result;
        } catch (Exception e) {
            // 记录失败操作
            auditService.recordAudit(username, module, operation, "FAILURE", e.getMessage());
            throw e;
        }
    }
}
```

---

## 🔧 安全配置检查清单

### 开发环境
- [ ] 禁用默认账户和密码
- [ ] 配置HTTPS证书
- [ ] 设置安全响应头
- [ ] 配置会话超时
- [ ] 启用CSRF保护

### 生产环境
- [ ] 更新所有依赖到最新安全版本
- [ ] 配置防火墙规则
- [ ] 启用安全监控和告警
- [ ] 定期安全扫描
- [ ] 备份和恢复测试

### 代码审查
- [ ] 检查SQL注入风险
- [ ] 验证XSS防护
- [ ] 确认权限控制正确
- [ ] 检查敏感信息泄露
- [ ] 验证加密算法使用

---

*最后更新：2024年12月 | 维护团队：OTO项目组安全团队*