# OTO-Home 后端API开发规范

> **适用范围**：oto-admin 和 oto-front 模块的API开发
> **技术栈**：Spring Boot + MyBatis-Plus + Redis

---

## 🎯 API设计原则

### RESTful API规范
- **资源命名**：使用名词复数形式，如 `/api/users`、`/api/orders`
- **HTTP方法**：GET（查询）、POST（创建）、PUT（更新）、DELETE（删除）
- **状态码**：正确使用HTTP状态码表示操作结果
- **版本控制**：通过URL路径进行版本控制，如 `/api/v1/users`

### 统一响应格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-12-21T10:30:00Z"
}
```

---

## 🔧 Controller层规范

### 命名规范
- **类命名**：`{业务模块}Controller`，如 `UserController`、`OrderController`
- **方法命名**：动词+名词，如 `getUser`、`createOrder`、`updateProfile`
- **路径映射**：`@RequestMapping("/api/{module}")`

### 参数验证
```java
@PostMapping("/users")
public R<UserVO> createUser(@Valid @RequestBody UserCreateDTO userDTO) {
    // 业务逻辑
}
```

### 异常处理
- 使用 `@ControllerAdvice` 全局异常处理
- 自定义业务异常类继承 `RuntimeException`
- 统一异常响应格式

---

## 🏗️ Service层规范

### 接口设计
- **接口命名**：`I{业务模块}Service`
- **实现类命名**：`{业务模块}ServiceImpl`
- **方法命名**：业务语义化，如 `registerUser`、`processOrder`

### 事务管理
```java
@Transactional(rollbackFor = Exception.class)
public void processBusinessLogic() {
    // 业务逻辑
}
```

### 缓存策略
- 使用 `@Cacheable`、`@CacheEvict` 注解
- 缓存键命名规范：`{module}:{operation}:{id}`
- 合理设置缓存过期时间

---

## 🗄️ 数据访问层规范

### Mapper接口
- 继承 `BaseMapper<T>` 获得基础CRUD操作
- 自定义查询方法使用 `@Select`、`@Update` 等注解
- 复杂查询使用XML映射文件

### 实体类规范
```java
@TableName("sys_user")
public class User extends BaseEntity {
    @TableId(type = IdType.ASSIGN_ID)
    private Long userId;
    
    @TableField("user_name")
    private String userName;
}
```

---

## 🔐 安全规范

### 认证授权
- 使用JWT Token进行身份认证
- 实现基于角色的权限控制（RBAC）
- API接口添加 `@PreAuthorize` 权限注解

### 数据验证
- 所有输入参数必须进行验证
- 使用Bean Validation注解
- 防止SQL注入和XSS攻击

### 敏感数据处理
- 密码使用BCrypt加密
- 敏感信息不记录到日志
- 数据传输使用HTTPS

---

## 📊 性能优化

### 数据库优化
- 合理使用索引
- 避免N+1查询问题
- 使用分页查询处理大数据量

### 缓存策略
- 热点数据使用Redis缓存
- 实现缓存预热和更新机制
- 避免缓存雪崩和穿透

### 异步处理
- 耗时操作使用异步处理
- 合理使用线程池
- 实现消息队列处理

---

## 📝 文档规范

### API文档
- 使用Swagger/OpenAPI生成API文档
- 详细描述请求参数和响应格式
- 提供示例代码和测试用例

### 代码注释
- 类和方法必须添加JavaDoc注释
- 复杂业务逻辑添加行内注释
- 注释内容准确描述功能和用途

---

*最后更新：2024年12月 | 维护团队：OTO项目组后端团队*