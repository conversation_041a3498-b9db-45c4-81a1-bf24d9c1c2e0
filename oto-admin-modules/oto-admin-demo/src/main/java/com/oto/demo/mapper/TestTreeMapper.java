package com.oto.demo.mapper;

import com.oto.common.mybatis.annotation.DataColumn;
import com.oto.common.mybatis.annotation.DataPermission;
import com.oto.common.mybatis.core.mapper.BaseMapperPlus;
import com.oto.demo.domain.TestTree;
import com.oto.demo.domain.vo.TestTreeVo;

/**
 * 测试树表Mapper接口
 *
 * <AUTHOR> Li
 * @date 2021-07-26
 */
@DataPermission({
    @DataColumn(key = "deptName", value = "dept_id"),
    @DataColumn(key = "userName", value = "user_id")
})
public interface TestTreeMapper extends BaseMapperPlus<TestTree, TestTreeVo> {

}
