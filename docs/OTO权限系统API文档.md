# OTO权限系统API文档

## 📋 接口概览

本文档详细描述了OTO多角色权限系统提供的所有API接口，包括权限验证、角色管理、用户权限查询等功能。

## 🔐 认证说明

所有API接口都需要在请求头中携带有效的访问令牌：

```http
Authorization: Bearer {access_token}
```

## 📚 接口分类

### 1. 用户权限查询接口

#### 1.1 获取用户角色权限

**接口地址**: `GET /api/member/permission/role/{userId}`

**接口描述**: 获取指定用户的所有角色权限

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | Long | 是 | 用户ID |

**响应示例**:

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    "member:profile:view",
    "member:course:view",
    "member:yoga:create",
    "member:yoga:edit",
    "member:schedule:manage"
  ]
}
```

#### 1.2 获取用户菜单权限

**接口地址**: `GET /api/member/permission/menu/{userId}`

**接口描述**: 获取指定用户的菜单访问权限

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | Long | 是 | 用户ID |

**响应示例**:

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    "member:dashboard",
    "member:profile",
    "member:courses",
    "member:yoga:management"
  ]
}
```

#### 1.3 检查用户身份类型

**接口地址**: `GET /api/member/permission/identity/{userId}`

**接口描述**: 检查用户是否为普通用户或服务提供者

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | Long | 是 | 用户ID |

**响应示例**:

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "isConsumer": true,
    "isServiceProvider": true,
    "serviceProviderTypes": [
      "yoga_teacher",
      "fitness_coach"
    ]
  }
}
```

#### 1.4 获取当前用户权限信息

**接口地址**: `GET /api/member/permission/current`

**接口描述**: 获取当前登录用户的完整权限信息

**响应示例**:

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "userId": 1001,
    "username": "yoga_teacher_001",
    "rolePermissions": [
      "member:profile:view",
      "member:course:view",
      "member:yoga:create",
      "member:yoga:edit",
      "member:yoga:delete",
      "member:schedule:manage"
    ],
    "menuPermissions": [
      "member:dashboard",
      "member:profile",
      "member:courses",
      "member:yoga:management",
      "member:schedule:management"
    ],
    "isConsumer": true,
    "isServiceProvider": true,
    "serviceProviderTypes": [
      "yoga_teacher"
    ]
  }
}
```

### 2. 角色管理接口

#### 2.1 检查服务提供者角色

**接口地址**: `POST /api/member/permission/check-provider-role`

**接口描述**: 检查用户是否具有指定的服务提供者角色

**请求体**:

```json
{
  "userId": 1001,
  "serviceProviderRole": "yoga_teacher"
}
```

**响应示例**:

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "hasRole": true,
    "roleInfo": {
      "roleName": "yoga_teacher",
      "roleDisplayName": "瑜伽老师",
      "permissions": [
        "member:yoga:create",
        "member:yoga:edit",
        "member:yoga:delete",
        "member:schedule:manage"
      ]
    }
  }
}
```

#### 2.2 批量检查用户权限

**接口地址**: `POST /api/member/permission/batch-check`

**接口描述**: 批量检查多个用户的权限信息

**请求体**:

```json
{
  "userIds": [1001, 1002, 1003],
  "checkType": "service_provider" // 可选值: "consumer", "service_provider", "all"
}
```

**响应示例**:

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "1001": {
      "isConsumer": true,
      "isServiceProvider": true,
      "serviceProviderTypes": ["yoga_teacher"]
    },
    "1002": {
      "isConsumer": true,
      "isServiceProvider": false,
      "serviceProviderTypes": []
    },
    "1003": {
      "isConsumer": true,
      "isServiceProvider": true,
      "serviceProviderTypes": ["fitness_coach", "nutritionist"]
    }
  }
}
```

### 3. 权限验证接口

#### 3.1 验证接口访问权限

**接口地址**: `POST /api/member/permission/validate-access`

**接口描述**: 验证用户是否有权限访问指定的接口

**请求体**:

```json
{
  "userId": 1001,
  "resource": "/api/yoga/classes",
  "action": "GET",
  "requiredPermissions": [
    "member:yoga:view"
  ]
}
```

**响应示例**:

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "hasAccess": true,
    "reason": "用户具有所需权限",
    "matchedPermissions": [
      "member:yoga:view"
    ]
  }
}
```

#### 3.2 验证多角色权限

**接口地址**: `POST /api/member/permission/validate-multi-role`

**接口描述**: 验证用户是否满足多角色权限要求

**请求体**:

```json
{
  "userId": 1001,
  "requiredRoles": ["yoga_teacher", "admin"],
  "logical": "OR", // 可选值: "AND", "OR"
  "includeConsumer": false
}
```

**响应示例**:

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "hasAccess": true,
    "matchedRoles": ["yoga_teacher"],
    "userRoles": ["consumer", "yoga_teacher"],
    "validationResult": "用户满足OR逻辑的角色要求"
  }
}
```

### 4. 业务场景接口示例

#### 4.1 瑜伽课程管理接口

**接口地址**: `GET /api/yoga/classes`

**权限要求**: `@RequireServiceProvider(ServiceProviderType.YOGA_TEACHER)`

**接口描述**: 获取瑜伽课程列表（仅瑜伽老师可访问）

**响应示例**:

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "total": 25,
    "rows": [
      {
        "id": 1,
        "className": "初级哈他瑜伽",
        "instructor": "张老师",
        "duration": 60,
        "maxStudents": 20,
        "currentStudents": 15,
        "status": "active"
      }
    ]
  }
}
```

#### 4.2 创建瑜伽课程接口

**接口地址**: `POST /api/yoga/classes`

**权限要求**: `@RequireServiceProvider(ServiceProviderType.YOGA_TEACHER)`

**接口描述**: 创建新的瑜伽课程

**请求体**:

```json
{
  "className": "高级阿斯汤加瑜伽",
  "description": "适合有一定基础的学员",
  "duration": 90,
  "maxStudents": 15,
  "price": 150.00,
  "schedule": {
    "dayOfWeek": "MONDAY",
    "startTime": "19:00",
    "endTime": "20:30"
  }
}
```

**响应示例**:

```json
{
  "code": 200,
  "msg": "课程创建成功",
  "data": {
    "classId": 26,
    "className": "高级阿斯汤加瑜伽",
    "instructorId": 1001,
    "createdAt": "2025-08-15T10:30:00"
  }
}
```

#### 4.3 管理员审核接口

**接口地址**: `POST /api/admin/yoga/classes/{classId}/approve`

**权限要求**: `@RequireMultiRole(value = {"admin", "yoga_teacher"}, logical = Logical.AND)`

**接口描述**: 审核瑜伽课程（需要同时具有管理员和瑜伽老师角色）

**请求体**:

```json
{
  "approved": true,
  "comment": "课程内容符合标准，批准上线"
}
```

**响应示例**:

```json
{
  "code": 200,
  "msg": "审核完成",
  "data": {
    "classId": 26,
    "status": "approved",
    "approvedBy": 1001,
    "approvedAt": "2025-08-15T10:35:00",
    "comment": "课程内容符合标准，批准上线"
  }
}
```

## 🚨 错误码说明

### 权限相关错误码

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 403001 | 权限不足 | 用户没有访问该资源的权限 |
| 403002 | 服务提供者身份验证失败 | 用户不具有所需的服务提供者身份 |
| 403003 | 多角色验证失败 | 用户不满足多角色权限要求 |
| 403004 | 普通用户权限不足 | 普通用户无法访问该资源 |
| 403005 | 角色权限过期 | 用户角色权限已过期，需要重新认证 |

### 业务相关错误码

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 400001 | 参数验证失败 | 请求参数不符合要求 |
| 404001 | 用户不存在 | 指定的用户ID不存在 |
| 404002 | 角色不存在 | 指定的角色不存在 |
| 500001 | 权限服务异常 | 权限服务内部错误 |

## 📝 接口调用示例

### JavaScript/TypeScript 示例

```typescript
// 获取当前用户权限信息
async function getCurrentUserPermissions() {
  try {
    const response = await fetch('/api/member/permission/current', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    const result = await response.json();
    
    if (result.code === 200) {
      console.log('用户权限信息:', result.data);
      return result.data;
    } else {
      console.error('获取权限失败:', result.msg);
    }
  } catch (error) {
    console.error('请求异常:', error);
  }
}

// 检查用户是否为瑜伽老师
async function checkYogaTeacherRole(userId: number) {
  try {
    const response = await fetch('/api/member/permission/check-provider-role', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        userId: userId,
        serviceProviderRole: 'yoga_teacher'
      })
    });
    
    const result = await response.json();
    return result.code === 200 && result.data.hasRole;
  } catch (error) {
    console.error('角色检查异常:', error);
    return false;
  }
}
```

### Java 示例

```java
@RestController
@RequestMapping("/api/example")
@RequiredArgsConstructor
public class ExampleController {
    
    private final IOtoMemberPermissionService permissionService;
    
    /**
     * 示例：根据用户权限返回不同的数据
     */
    @GetMapping("/dashboard")
    public R<DashboardVo> getDashboard() {
        DashboardVo dashboard = new DashboardVo();
        
        // 检查用户身份，返回对应的仪表板数据
        if (permissionService.isServiceProvider()) {
            // 服务提供者仪表板
            dashboard = buildProviderDashboard();
            
            // 根据具体的服务提供者类型添加特定功能
            Set<String> providerTypes = permissionService.getServiceProviderTypes();
            if (providerTypes.contains("yoga_teacher")) {
                dashboard.addYogaManagementModule();
            }
            if (providerTypes.contains("fitness_coach")) {
                dashboard.addFitnessManagementModule();
            }
        } else {
            // 普通用户仪表板
            dashboard = buildConsumerDashboard();
        }
        
        return R.ok(dashboard);
    }
    
    /**
     * 示例：权限验证失败的处理
     */
    @RequireServiceProvider(ServiceProviderType.YOGA_TEACHER)
    @PostMapping("/yoga/advanced-class")
    public R<Void> createAdvancedYogaClass(@RequestBody YogaClassBo bo) {
        // 这个方法只有瑜伽老师可以访问
        // 如果权限验证失败，会自动抛出异常，返回403错误
        
        // 业务逻辑
        yogaService.createAdvancedClass(bo);
        
        return R.ok();
    }
}
```

### Python 示例

```python
import requests
import json

class OTOPermissionClient:
    def __init__(self, base_url, access_token):
        self.base_url = base_url
        self.headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }
    
    def get_current_user_permissions(self):
        """获取当前用户权限信息"""
        url = f'{self.base_url}/api/member/permission/current'
        response = requests.get(url, headers=self.headers)
        
        if response.status_code == 200:
            result = response.json()
            if result['code'] == 200:
                return result['data']
        
        return None
    
    def check_service_provider_role(self, user_id, role):
        """检查服务提供者角色"""
        url = f'{self.base_url}/api/member/permission/check-provider-role'
        data = {
            'userId': user_id,
            'serviceProviderRole': role
        }
        
        response = requests.post(url, headers=self.headers, json=data)
        
        if response.status_code == 200:
            result = response.json()
            return result['code'] == 200 and result['data']['hasRole']
        
        return False
    
    def validate_multi_role_access(self, user_id, required_roles, logical='AND'):
        """验证多角色权限"""
        url = f'{self.base_url}/api/member/permission/validate-multi-role'
        data = {
            'userId': user_id,
            'requiredRoles': required_roles,
            'logical': logical
        }
        
        response = requests.post(url, headers=self.headers, json=data)
        
        if response.status_code == 200:
            result = response.json()
            return result['code'] == 200 and result['data']['hasAccess']
        
        return False

# 使用示例
client = OTOPermissionClient('http://localhost:8081', 'your_access_token')

# 获取当前用户权限
permissions = client.get_current_user_permissions()
print(f'用户权限: {permissions}')

# 检查是否为瑜伽老师
is_yoga_teacher = client.check_service_provider_role(1001, 'yoga_teacher')
print(f'是否为瑜伽老师: {is_yoga_teacher}')

# 验证多角色权限
has_admin_access = client.validate_multi_role_access(
    1001, 
    ['admin', 'yoga_teacher'], 
    'OR'
)
print(f'是否有管理员权限: {has_admin_access}')
```

## 🔧 SDK 集成

### 前端权限组件

```vue
<!-- Vue.js 权限组件示例 -->
<template>
  <div>
    <!-- 根据权限显示不同内容 -->
    <div v-if="hasPermission('member:yoga:create')">
      <button @click="createYogaClass">创建瑜伽课程</button>
    </div>
    
    <!-- 根据角色显示不同内容 -->
    <div v-if="isServiceProvider">
      <h3>服务提供者专区</h3>
      <div v-if="hasRole('yoga_teacher')">
        <yoga-management-panel />
      </div>
      <div v-if="hasRole('fitness_coach')">
        <fitness-management-panel />
      </div>
    </div>
    
    <!-- 普通用户内容 -->
    <div v-else>
      <h3>会员专区</h3>
      <member-dashboard />
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      userPermissions: [],
      userRoles: [],
      isServiceProvider: false
    }
  },
  
  async mounted() {
    await this.loadUserPermissions();
  },
  
  methods: {
    async loadUserPermissions() {
      try {
        const response = await this.$http.get('/api/member/permission/current');
        if (response.data.code === 200) {
          const data = response.data.data;
          this.userPermissions = data.rolePermissions;
          this.userRoles = data.serviceProviderTypes;
          this.isServiceProvider = data.isServiceProvider;
        }
      } catch (error) {
        console.error('加载用户权限失败:', error);
      }
    },
    
    hasPermission(permission) {
      return this.userPermissions.includes(permission);
    },
    
    hasRole(role) {
      return this.userRoles.includes(role);
    },
    
    async createYogaClass() {
      // 创建瑜伽课程的逻辑
    }
  }
}
</script>
```

## 📊 性能监控

### 权限查询性能指标

建议监控以下指标：

- 权限查询响应时间（目标：< 100ms）
- 权限验证成功率（目标：> 99.9%）
- 缓存命中率（目标：> 95%）
- 并发权限验证处理能力

### 性能优化建议

1. **启用权限缓存**：减少数据库查询次数
2. **批量权限查询**：避免N+1查询问题
3. **权限预加载**：在用户登录时预加载常用权限
4. **异步权限更新**：权限变更时异步更新缓存

---

**📞 技术支持**: 如有API使用问题，请联系开发团队。

**🔗 相关文档**:
- [权限系统使用文档](./OTO多角色权限系统使用文档.md)
- [在线API文档](http://localhost:8081/doc.html)
- [Postman集合](./OTO-Permission-API.postman_collection.json)